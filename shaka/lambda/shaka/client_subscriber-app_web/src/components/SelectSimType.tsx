import { useEffect, useState } from 'react';
import { updateSubscriber } from 'src/api/data';
import { CheckIcon } from 'src/assets/icons/Check';
import { ClockIcon } from 'src/assets/icons/Clock';
import { LeafIcon } from 'src/assets/icons/Leaf';
import { LockerIcon } from 'src/assets/icons/Locker';
import Button from 'src/components/common/Button';
import ProgressLinks from 'src/components/common/ProgressLinks';
import CompatibilityCheckDrawer from 'src/components/sign-up/CompatibilityCheckDrawer';
import {
  getCorrelationId,
  getSimSettingsSelectedType,
  setSimSettingsSelectedType
} from 'src/config/localStorageActions';
import { useRequest } from 'src/hooks/useRequest';
import useSubscription from 'src/hooks/useSubscription';
import { twMerge } from 'tailwind-merge';

export type SimTypeOption = 'esim' | 'physical' | 'self-serve';

const optionStyles = 'w-full bg-white rounded-xl min-h-30 p-4 pt-6 relative';
const disabledStyles = 'opacity-50 hover:opacity-80';

const eSimCons = [
  {
    title: 'Set up right now, in minutes',
    description: 'We’ll walk you step-by-step through the process. ',
    icon: <ClockIcon className="size-7" />
  },
  {
    title: 'Stay more secure',
    description: 'Harder to hack if your phone gets stolen',
    icon: <LockerIcon className="size-7" />
  },
  {
    title: 'Do your bit for the planet',
    description: 'No plastic, no packaging, and no delivery emissions',
    icon: <LeafIcon className="size-7" />
  }
];

const disabled_physical_sim =
  import.meta.env.VITE_DISABLE_PHYSICAL_SIM === 'true';

export default function SelectSimType({
  onNextClick,
  persistStepStatus = true,
  progressLinksModalView
}: {
  onNextClick: (selectedSimType: SimTypeOption) => void;
  persistStepStatus?: boolean;
  progressLinksModalView?: boolean;
}) {
  const simPreselectedOption = getSimSettingsSelectedType() as SimTypeOption;
  const { run: runUpdateSubscriber } = useRequest(updateSubscriber);
  const { currentPlan, setSubscriber, setCurrentPlanByCorrelationId } =
    useSubscription();

  const correlationId = getCorrelationId();

  useEffect(() => {
    if (correlationId) {
      setCurrentPlanByCorrelationId(correlationId);
    }
  }, [correlationId, setCurrentPlanByCorrelationId]);

  const [selectedSimType, setSelectedSimType] = useState<SimTypeOption>(
    simPreselectedOption || 'esim'
  );

  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [isCompatibilityConfirmed, setIsCompatibilityConfirmed] =
    useState(false);

  const isNextDisabled =
    selectedSimType === 'esim' && !isCompatibilityConfirmed;

  const handleSimTypeChange = (type: SimTypeOption) => () => {
    setSelectedSimType(type);

    if (persistStepStatus) {
      setSimSettingsSelectedType(type);
    }
  };

  const handleSimTypeSubmit = () => {
    const simType = selectedSimType === 'esim' ? 'esim' : 'physical';

    runUpdateSubscriber({
      sim_type: simType,
      subscription_id: currentPlan.id
    }).then((data) => {
      setSubscriber(data);
    });

    onNextClick(selectedSimType);
  };

  const openCompatibilityCheck = () => {
    setIsDrawerOpen(true);
  };

  const compatibilityConfirmedToggle = () => {
    setIsCompatibilityConfirmed(!isCompatibilityConfirmed);
  };

  useEffect(() => {
    if (persistStepStatus) {
      setSimSettingsSelectedType(simPreselectedOption || 'esim');
    }
  }, []);

  return (
    <>
      <div className="space-y-6">
        <div
          className={twMerge(
            'w-full flex gap-4 items-center cursor-pointer',
            selectedSimType !== 'esim' && disabledStyles
          )}
          onClick={handleSimTypeChange('esim')}
        >
          <div className={twMerge(optionStyles, 'divide-y')}>
            <div className="text-left mb-5">
              <span className="font-bold text-xl">eSIM</span>
              <p className="text-gray-500 text-xs mt-1">Instant activation</p>
            </div>

            <div className="sim-label absolute top-2.5 right-2.5 text-white text-[10px] rounded-full px-2.5 py-0.5 uppercase font-semibold">
              Recommended
            </div>
            <div>
              <div className="pt-5 space-y-4 mb-5">
                {eSimCons.map(({ description, icon, title }) => (
                  <div
                    key={title}
                    className="flex items-center gap-5 text-left"
                  >
                    {icon}
                    <div className="">
                      <span className="block font-bold text-xs">{title}</span>
                      <p className="text-[10px]">{description}</p>
                    </div>
                  </div>
                ))}
              </div>
              <div
                className="rounded-lg border p-2 text-xs text-center"
                onClick={openCompatibilityCheck}
              >
                Please check that your device is{' '}
                <span className="underline text-green">eSIM compatible</span>
              </div>
              <div className="mt-4 relative">
                <Button
                  type="button"
                  color="black"
                  fullWidth
                  squared
                  fontSize="small"
                  onClick={compatibilityConfirmedToggle}
                >
                  <span
                    className={twMerge(
                      'absolute top-2 md:top-3 left-2 md:left-3 bg-[#404040] rounded-md text-white p-1.5 pointer-events-none',
                      isCompatibilityConfirmed && 'bg-green'
                    )}
                  >
                    <CheckIcon
                      className="size-3"
                      color={isCompatibilityConfirmed ? 'white' : '#808080'}
                    />
                  </span>
                  {isCompatibilityConfirmed
                    ? 'Compatibility confirmed'
                    : 'Confirm device compatibility'}
                </Button>
              </div>
            </div>
          </div>
        </div>

        <div
          className={twMerge(
            'w-full relative',
            selectedSimType !== 'physical' && disabledStyles,
            disabled_physical_sim ? 'pointer-events-none' : 'cursor-pointer'
          )}
          onClick={handleSimTypeChange('physical')}
        >
          <div
            className={twMerge(
              optionStyles,
              'flex flex-col h-full justify-center items-start pb-6'
            )}
          >
            <span className="font-bold text-xl">Physical SIM</span>
            <p className="text-gray-500 text-xs mt-1">3 day delivery</p>
            {disabled_physical_sim && (
              <p className="bg-gray-200 absolute top-2.5 right-2.5 text-[10px] rounded-full px-2.5 py-0.5 uppercase font-semibold">
                coming soon
              </p>
            )}
          </div>
        </div>

        <div
          className={twMerge(
            'w-full relative',
            selectedSimType !== 'self-serve' && disabledStyles,
            disabled_physical_sim ? 'pointer-events-none' : 'cursor-pointer'
          )}
          onClick={handleSimTypeChange('self-serve')}
        >
          <div
            className={twMerge(
              optionStyles,
              'flex flex-col h-full justify-center items-start pb-6'
            )}
          >
            <span className="font-bold text-xl">
              I already have a physical SIM
            </span>
            <p className="text-gray-500 text-xs mt-1">Activate now</p>

            {disabled_physical_sim && (
              <p className="bg-gray-200 absolute top-2.5 right-2.5 text-[10px] rounded-full px-2.5 py-0.5 uppercase font-semibold">
                coming soon
              </p>
            )}
          </div>
        </div>
      </div>
      <ProgressLinks
        hideBackButton
        onNextClick={handleSimTypeSubmit}
        disabledNext={isNextDisabled}
        isModalView={progressLinksModalView}
      />

      <CompatibilityCheckDrawer
        title="Compatibility check"
        isOpen={isDrawerOpen}
        onClose={() => setIsDrawerOpen(false)}
      />
    </>
  );
}
