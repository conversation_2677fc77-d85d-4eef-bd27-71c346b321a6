import { Popover } from '@headlessui/react';
import { useEffect, useState } from 'react';
import useSubscription from 'src/hooks/useSubscription';
import { SubscriberPlan } from 'src/types/subscriber';
import { twMerge } from 'tailwind-merge';

export default function EsimPlanDropdown({
  onChange
}: {
  onChange: (plan: SubscriberPlan) => void;
}) {
  const { currentPlan, subscriberPlanSimList } = useSubscription();

  const options = subscriberPlanSimList
    ?.filter((item) => item.type === 'plan' && item.data.sim_type === 'esim')
    .map((item) => {
      const label =
        item.type === 'plan'
          ? item.data.user_subscription_name ||
            'Plan ' + (item.initialIndex + 1)
          : item.data.custom_name || 'Travel Plan ' + (item.initialIndex + 1);
      const value = item.id;

      return {
        id: item.id,
        label,
        value
      };
    });

  const [selectedPlan, setSelectedPlan] = useState(
    options.find((item) => item.id === currentPlan.id) || options[0]
  );

  useEffect(() => {
    const newPlan = subscriberPlanSimList.find(
      (item) => item.id === selectedPlan.id
    )?.data;

    onChange(newPlan as SubscriberPlan);
  }, [selectedPlan]);

  return (
    <Popover className="relative z-50">
      {({ open }) => (
        <>
          {open && (
            <div className="background fixed bg-black/30 w-screen h-screen left-0 top-0" />
          )}

          <Popover.Button
            className={twMerge(
              'rounded-full bg-white px-4 py-1 text-xs focus:outline-none relative'
            )}
          >
            {selectedPlan.label}
          </Popover.Button>

          <Popover.Panel className="absolute z-100 left-1/2 -translate-x-1/2 top-[32px]">
            {({ close }) => (
              <div className="flex flex-col gap-2 p-4 bg-white rounded-3xl min-w-[260px] shadow-2xl">
                {options?.map((option) => (
                  <button
                    key={option.id}
                    onClick={() => {
                      setSelectedPlan(option);
                      close();
                    }}
                    className={twMerge(
                      'text-sm text-left bg-[#F3F3F3] px-3.5 py-3.5 rounded-2xl border',
                      option.id === selectedPlan.id
                        ? 'border-black'
                        : 'border-transparent'
                    )}
                  >
                    {option.label}
                  </button>
                ))}
              </div>
            )}
          </Popover.Panel>
        </>
      )}
    </Popover>
  );
}
