import React, { useEffect, useRef, useState } from 'react';
import Slider from 'react-slick';
import useSubscription from 'src/hooks/useSubscription';
import PlanSettingsCard from '../PlanSettingsCard';
import NewSubscriptionDrawer from './NewSubscriptionDrawer';
import SubscriptionCustomName from '../SubscriptionCustomName';
import './styles.css';
import TravelSimCard from '../TravelSimCard';
import { AddPlanIcon } from 'src/assets/icons/AddPlan';
import { Link, useNavigate } from '@tanstack/react-router';
import { ROUTES } from 'src/config/routes';
import { SubscriberPlan } from 'src/types/subscriber';
import { defaultRoamingSimName, defaultSimName } from 'src/helpers';

const newPlanButtonStyles =
  'flex flex-col items-center justify-center text-center max-w-[150px] group hover:bg-black/5 pt-10 px-2 mb-10 rounded-2xl h-full';

const settings = {
  dots: true,
  infinite: false,
  speed: 250,
  slidesToShow: 1,
  slideToScroll: 1,
  arrows: false,
  dotsClass: 'slick-dots',
  centerMode: true,
  centerPadding: '16px'
};

export default function SubscriptionSwiper({
  onSwipe
}: {
  onSwipe?: (index: number) => void;
}) {
  const navigate = useNavigate();
  const searchParams = new URLSearchParams(window.location.search);
  const newSubscriptionStep = searchParams.get('new-subscription');

  const sliderRef = useRef<Slider | null>(null);
  const {
    subscriber,
    currentPlanIndex,
    setCurrentPlanIndex,
    activeAndRecentlyExpiredRoamingSims,
    hasEsim
  } = useSubscription();

  const [expectedIndex, setExpectedIndex] = useState(currentPlanIndex);
  const hasPlans = !!subscriber?.plans?.length;
  const roamingEsims = subscriber?.roaming_esims ?? [];
  const travelSimPlanIndex = hasPlans
    ? currentPlanIndex - subscriber.plans.length
    : 0;
  const selectedRoamingESim = roamingEsims[travelSimPlanIndex];
  const customLabelName = selectedRoamingESim?.custom_name;

  const [isSubscriptionDrawerOpen, setIsSubscriptionDrawerOpen] =
    useState(false);
  const [closeDrawerEnabled, setCloseDrawerEnabled] = useState(true);
  const hasTravelSim = Boolean(subscriber?.roaming_esims?.length);

  const handleDrawerClose = () => {
    if (closeDrawerEnabled) {
      setIsSubscriptionDrawerOpen(false);
      navigate({
        search: {},
        replace: true
      });
    }
  };

  useEffect(() => {
    if (newSubscriptionStep) {
      setIsSubscriptionDrawerOpen(true);
    }
  }, [newSubscriptionStep]);

  useEffect(() => {
    console.log(sliderRef, expectedIndex, currentPlanIndex);
    if (!!sliderRef.current && expectedIndex !== currentPlanIndex) {
      sliderRef.current.slickGoTo(currentPlanIndex);
    }
  }, [currentPlanIndex, expectedIndex]);

  return (
    <div className="subscription-swiper">
      <div className="w-screen max-w-md -translate-x-5 relative">
        <Slider
          {...settings}
          ref={sliderRef}
          onSwipe={() => {
            if (sliderRef.current)
              sliderRef.current.slickGoTo(currentPlanIndex);
          }}
          initialSlide={currentPlanIndex}
          afterChange={setCurrentPlanIndex}
          beforeChange={(_, newIndex) => {
            setExpectedIndex(newIndex);
            if (onSwipe) {
              onSwipe(newIndex);
            }
          }}
        >
          {subscriber?.plans?.map((plan, index) => (
            <SubscriptionCard
              key={plan.id}
              esimStatus={plan.sim_type === 'esim' ? plan.esim_status : 'SIM'}
              subscriptionCustomName={
                <SubscriptionCustomName
                  withPlanNameUpdate
                  planName={
                    plan.user_subscription_name ||
                    `${defaultSimName} ${index + 1}`
                  }
                />
              }
            >
              <PlanSettingsCard plan={plan} />
            </SubscriptionCard>
          ))}
          {Boolean(activeAndRecentlyExpiredRoamingSims?.length) &&
            activeAndRecentlyExpiredRoamingSims.map((roaming_sim, index) => (
              <SubscriptionCard
                key={roaming_sim.id}
                esimStatus={roaming_sim.esim_status}
                subscriptionCustomName={
                  <SubscriptionCustomName
                    withPlanNameUpdate
                    planName={
                      customLabelName || `${defaultRoamingSimName} ${index + 1}`
                    }
                    travelSim
                  />
                }
              >
                <TravelSimCard {...roaming_sim} plain />
              </SubscriptionCard>
            ))}

          <div>
            <div className="mx-1">
              <p className="h-[70px]">&nbsp;</p>

              <div className="plan-card-wrapper">
                <div className="plan-card-detailed bg-[#C9C9C9] absolute inset-0 flex items-center justify-around text-sm font-semibold px-[5%] pt-10">
                  {hasEsim && (
                    <Link
                      to={ROUTES.Settings}
                      search={{ 'travel-esim': 1, action: 'getTravelEsim' }}
                      className={newPlanButtonStyles}
                    >
                      <AddPlanIcon className="size-11 transition" />
                      <span className="text-xs mt-6">
                        Get a travel eSIM{' '}
                        {hasTravelSim ? (
                          <span className="font-normal">
                            (to&nbsp;manage multiple roaming eSIMs)
                          </span>
                        ) : (
                          <span className="font-normal">
                            (to&nbsp;roam outside of&nbsp;the&nbsp;EU)
                          </span>
                        )}
                      </span>
                    </Link>
                  )}
                  <button
                    onClick={() => setIsSubscriptionDrawerOpen(true)}
                    className={newPlanButtonStyles}
                  >
                    <AddPlanIcon className="size-11" />
                    <span className="text-xs mt-6">
                      Add a UK SIM{' '}
                      <span className="font-normal">
                        (to&nbsp;manage multiple UK plans)
                      </span>
                    </span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </Slider>
      </div>
      <NewSubscriptionDrawer
        isOpen={isSubscriptionDrawerOpen}
        onClose={handleDrawerClose}
        title="ADD UK SIM"
        initialStep={newSubscriptionStep}
        allowDrawerToClose={setCloseDrawerEnabled}
        disableClose={!closeDrawerEnabled}
      />
    </div>
  );
}

interface SubscriptionCardProps extends React.PropsWithChildren {
  subscriptionCustomName: React.ReactNode;
  esimStatus: SubscriberPlan['esim_status'];
}

function SubscriptionCard({
  subscriptionCustomName,
  children,
  esimStatus
}: SubscriptionCardProps) {
  return (
    <div>
      <div className="mx-1">
        <p className="my-2 text-center">{subscriptionCustomName}</p>
        {esimStatus && (
          <h3 className="text-sm text-center mb-2 text-[#C9C9C9]">
            {esimStatus}
          </h3>
        )}
        {children}
      </div>
    </div>
  );
}
