import { useEffect, useMemo, useState } from 'react';
import withDrawer, { WithDrawerProps } from 'src/hocs/withDrawer';
import ExplorePlans from '../ExplorePlans';
import PlanConfirmation from '../PlanConfirmation';
import usePlans from 'src/hooks/usePlans';
import ProgressLinks from '../common/ProgressLinks';
import EsimSettings from '../EsimSettings';
import SelectSimType, { SimTypeOption } from '../SelectSimType';
import {
  removeNewSubscriptionStep,
  setNewSubscriptionStep
} from 'src/config/localStorageActions';
import { useNavigate } from '@tanstack/react-router';
import AddressDetails from '../Address';
import SimSelfActivation from '../SimActivation';
import { CheckRoundedFilledIcon } from 'src/assets/icons/CheckRoundedFilled';
import Button from '../common/Button';
import NewPlanPayment from './NewPlanPayment';
import { twMerge } from 'tailwind-merge';

export enum Step {
  ExplorePlans,
  PlanConfirmation,
  Payment,
  SelectSimType,
  EsimSettings,
  AddressDetails,
  SimActivation,
  Congratulations
}

function NewSubscriptionContent({
  initialStep,
  allowDrawerToClose,
  closeDialog
}: {
  initialStep?: string | null;
  allowDrawerToClose: (value: boolean) => void;
} & WithDrawerProps) {
  const navigate = useNavigate();

  const { plans } = usePlans();
  const [planId, setPlanId] = useState<number | null>(null);
  const [stepId, setStepId] = useState(
    initialStep ? Number(initialStep) : Step.ExplorePlans
  );
  const [isOptIn, setIsOptIn] = useState(true);

  const selectedPlan = useMemo(() => {
    const selectedPlan = plans.find((plan) => plan.clientPlan.id === planId);

    return selectedPlan;
  }, [plans, planId]);

  const handlePlanConfirm = (id: number) => {
    setPlanId(id);
    setStepId(Step.PlanConfirmation);
  };

  const handleSimTypeSubmit = (selectedSimType: SimTypeOption) => {
    let nextToStep: number = Step.EsimSettings;
    if (selectedSimType === 'physical') {
      nextToStep = Step.AddressDetails;
    }
    if (selectedSimType === 'self-serve') {
      nextToStep = Step.SimActivation;
    }

    setStepId(nextToStep);
  };

  useEffect(() => {
    if (stepId > Step.Payment) {
      setNewSubscriptionStep(stepId);
      allowDrawerToClose(false);
    }

    if (stepId === Step.Congratulations || stepId === Step.EsimSettings) {
      removeNewSubscriptionStep();
      allowDrawerToClose(true);
      navigate({
        search: {},
        replace: true
      });
    } else {
      navigate({
        search: { 'new-subscription': stepId },
        replace: true
      });
    }
  }, [stepId, plans.length, allowDrawerToClose, navigate]);

  return (
    <div
      className={twMerge(
        // possible fix for double scroll - cannot reproduce on my end
        'h-[80dvh] px-5 relative',
        stepId === Step.EsimSettings && '-mb-5 h-[calc(80dvh_+_5rem)]'
      )}
    >
      {stepId === Step.ExplorePlans && (
        <div>
          {/*<div className="pb-28">*/}
          {/*for future reference if it break something*/}
          <ExplorePlans
            hideBackButton
            onPlanConfirm={handlePlanConfirm}
            progressLinksModalView
          />
        </div>
      )}
      {stepId === Step.PlanConfirmation && (
        <div className="relative mt-[22px]">
          <PlanConfirmation
            isOptIn={isOptIn}
            selectedPlan={selectedPlan}
            onOptInChange={setIsOptIn}
          />
          <ProgressLinks
            onBackClick={() => setStepId(Step.ExplorePlans)}
            onNextClick={() => setStepId(Step.Payment)}
            isModalView
          />
        </div>
      )}
      {stepId === Step.Payment && (
        <NewPlanPayment planId={planId!} returnUrl="/new-subscription/return" />
      )}
      {stepId === Step.SelectSimType && (
        <SelectSimType
          onNextClick={handleSimTypeSubmit}
          persistStepStatus={false}
          progressLinksModalView
        />
      )}

      {stepId === Step.EsimSettings && (
        <div className="h-[calc(80dvh_+_1.3rem)]">
          <EsimSettings
            onShareEsim={() => setStepId(Step.Congratulations)}
            onBackClick={() => setStepId(Step.SelectSimType)}
            onNextClick={() => setStepId(Step.Congratulations)}
            progressLinksModalView
          />
        </div>
      )}
      {stepId === Step.AddressDetails && (
        <AddressDetails
          onSubmitSuccess={() => setStepId(Step.Congratulations)}
          onBackClick={() => setStepId(Step.SelectSimType)}
          isModalView
        />
      )}
      {stepId === Step.SimActivation && (
        <div>
          <SimSelfActivation
            onSubmitSuccess={() => setStepId(Step.Congratulations)}
            onBackClick={() => setStepId(Step.SelectSimType)}
            isModalView
          />
        </div>
      )}
      {stepId === Step.Congratulations && (
        <div className="grow flex flex-col justify-center items-center gap-8 absolute inset-0 px-5 pb-16">
          <div className="validation valid">
            <CheckRoundedFilledIcon className="size-12" />
          </div>
          <span className="font-semibold mb-4">And you are all done!</span>
          <Button onClick={closeDialog}>Go to dashboard</Button>
        </div>
      )}
    </div>
  );
}

const NewSubscriptionDrawer = withDrawer(NewSubscriptionContent);

export default NewSubscriptionDrawer;
