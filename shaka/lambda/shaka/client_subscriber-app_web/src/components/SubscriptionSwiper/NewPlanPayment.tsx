import { useContext, useEffect, useState } from 'react';
import { getClientSecretSignUp } from 'src/api/stripe';
import { StripeContext } from 'src/context/StripeContext';
import {
  EmbeddedCheckout,
  EmbeddedCheckoutProvider
} from '@stripe/react-stripe-js';
import { StripeElementsOptions } from '@stripe/stripe-js';
import FullPageLoader from '../common/FullPageLoader';

export default function NewPlanPayment({
  planId,
  returnUrl
}: {
  planId: number;
  returnUrl: string;
}) {
  const [options, setOptions] = useState<StripeElementsOptions | null>(null);
  const { stripePromise } = useContext(StripeContext);

  useEffect(() => {
    if (planId) {
      const clientSecret = localStorage.getItem('client_secret');
      const sessionExpiresAt = localStorage.getItem('session_expires_at');
      const isSecretValid =
        sessionExpiresAt && new Date(Number(sessionExpiresAt)) > new Date();

      if (clientSecret && isSecretValid) {
        setOptions({
          clientSecret
        });
        return;
      }

      getClientSecretSignUp(planId?.toString() ?? '', returnUrl).then((res) => {
        setOptions({
          clientSecret: res.client_secret
        });
        localStorage.setItem('client_secret', res.client_secret);
      });
    }
  }, [planId]);

  if (!options) {
    return (
      <div className="w-full h-full">
        <FullPageLoader />
      </div>
    );
  }

  return (
    <EmbeddedCheckoutProvider stripe={stripePromise} options={options}>
      <EmbeddedCheckout />
    </EmbeddedCheckoutProvider>
  );
}
