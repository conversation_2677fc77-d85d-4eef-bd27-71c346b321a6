import { useState } from 'react';
import { activateSim } from 'src/api/subscription';
import ErrorText from 'src/components/common/ErrorText';
import InputBlock from 'src/components/common/InputBlock';
import ProgressLinks, {
  ProgressLinksProps
} from 'src/components/common/ProgressLinks';
import SimCard from 'src/components/sim-activation/SimCard';
import { useRequest } from 'src/hooks/useRequest';
import useSubscription from 'src/hooks/useSubscription';

export default function SimSelfActivation({
  onSubmitSuccess,

  ...progressProps
}: {
  onSubmitSuccess: () => void;
} & ProgressLinksProps) {
  const { fetchSubscriber, subscriber, currentPlan } = useSubscription();
  const { run: runActivateSim, isLoading } = useRequest(activateSim);

  const [serialNumber, setSerialNumber] = useState('');
  const [error, setError] = useState('');

  const onSubmit = () => {
    runActivateSim({ code: serialNumber, subscription_id: currentPlan.id })
      .then(() => {
        fetchSubscriber();
        onSubmitSuccess();
      })
      .catch((error) => {
        setError(
          error.response?.data.error ||
            'Something went wrong. Please try later.'
        );
      });
  };

  return (
    <div>
      <div className="flex justify-center my-3">
        <SimCard backSide serialNumber={subscriber?.sim_fragment || ''} />
      </div>

      <div className="sim-activation-input max-w-[350px] mx-auto">
        <InputBlock label="Enter the last 8 digits of the SIM serial number:">
          <div className="flex justify-between items-center gap-4">
            <span className="text-[27px] font-light">
              {subscriber?.sim_fragment}
            </span>
            <>
              <input
                className="input input-code"
                placeholder="________"
                value={serialNumber}
                onChange={(e) => {
                  const value = e.target.value;
                  if (value.length <= 8) {
                    setSerialNumber(value);
                  }
                }}
                required
                type="number"
              />
            </>
          </div>
        </InputBlock>
      </div>
      <ErrorText>{error}</ErrorText>
      <ProgressLinks
        disabledNext={serialNumber.length !== 8}
        onNextClick={onSubmit}
        isLoading={isLoading}
        {...progressProps}
      />
    </div>
  );
}
