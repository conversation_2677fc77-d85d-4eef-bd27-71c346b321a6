import { Link } from '@tanstack/react-router';
import { isOpenId } from 'src/config/env-vars';
import { ROUTES } from 'src/config/routes';
import PlanSlideCard from 'src/components/PlanSlideCard';
import { ClientPlanDetails } from 'src/types/slide';
import useSubscription from 'src/hooks/useSubscription';
import { CheckRoundedFilledIcon } from 'src/assets/icons/CheckRoundedFilled';
import { twMerge } from 'tailwind-merge';

export default function PlanConfirmation({
  selectedPlan,
  isOptIn,
  onOptInChange
}: {
  isOptIn: boolean;
  onOptInChange: (newVal: boolean) => void;
  selectedPlan?: ClientPlanDetails;
}) {
  const { subscriber, subscriberPlanSimList } = useSubscription();

  return (
    <>
      <PlanSlideCard clientPlan={selectedPlan?.clientPlan} />

      <div className="mt-8 mx-3 space-y-6">
        <div className="flex justify-between text-xs">
          <span className="font-semibold">Customer</span>
          <span className="text-gray-500">{subscriber?.name}</span>
        </div>
        <div className="flex justify-between text-xs">
          <span className="font-semibold">Duration</span>
          <span className="text-gray-500">monthly rolling subscription</span>
        </div>
        <div className="flex justify-between text-xs">
          <span className="font-semibold">Terms & conditions</span>
          <Link to={ROUTES.Terms} className="text-gray-500 underline">
            View T&Cs
          </Link>
        </div>

        {subscriberPlanSimList.length === 0 && !isOpenId && (
          <div className="flex justify-between text-xs items-center">
            <span className="font-semibold">
              Opt-in for exclusive discounts & offers
            </span>
            <CheckRoundedFilledIcon
              className={twMerge(
                'size-5 md:hover:text-black/45 cursor-pointer transition-all',
                isOptIn ? 'text-black' : 'text-white'
              )}
              onClick={() => onOptInChange(!isOptIn)}
            />
          </div>
        )}
      </div>
    </>
  );
}
