import { Combobox, Tab } from '@headlessui/react';
import { Fragment, useMemo, useState } from 'react';
import { twMerge } from 'tailwind-merge';
import { Transition } from '@headlessui/react';
import { countries } from 'src/config/countries';
import { SearchIcon } from 'src/assets/icons/Search';
import * as countriesIcons from 'country-flag-icons/react/3x2';
import CountryOption from './CountryOption';
import CountryIcon from 'src/components/common/CountryIcon';
import TabTitle from 'src/components/common/TabTitle';
import useRoamingSim from 'src/hooks/useRoamingSim';
import { RoamingCountry, RoamingRegion } from 'src/types/roaming-sim';
import Loader from '../common/Loader';

export default function CountrySelection({
  selectedZone: selectedOption,
  isRegion,
  onSelect
}: {
  selectedZone?: RoamingCountry;
  isRegion?: boolean;
  onSelect: (value: RoamingCountry, isRegion: boolean) => void;
}) {
  const { roamingCountries, roamingRegions, mostPopularCountries } =
    useRoamingSim();

  const [query, setQuery] = useState('');
  const [selectedTab, setSelectedTab] = useState(isRegion ? 1 : 0);

  const [isOptionDropdownOpen, setIsOptionDropdownOpen] =
    useState(!selectedOption);

  const withRegions = roamingRegions && Object.keys(roamingRegions).length > 0;

  const SelectedCountryIcon =
    selectedOption?.value && selectedTab === 0
      ? countriesIcons[selectedOption?.value as keyof typeof countriesIcons]
      : null;

  const { searchCountryResults, mostPopularResult } = useMemo(() => {
    if (!roamingCountries) {
      return {
        searchCountryResults: [],
        mostPopularResult: []
      };
    }
    const roamingCountriesKeys = Object.keys(roamingCountries);
    const availableCountries = countries.filter((country) => {
      return roamingCountriesKeys?.includes(country.value);
    });

    const withoutMostPopularCountries = availableCountries.sort((a, b) =>
      a.label.localeCompare(b.label)
    );

    if (!query) {
      return {
        searchCountryResults: withoutMostPopularCountries,
        mostPopularResult: availableCountries.filter((country) =>
          mostPopularCountries.includes(country.value)
        )
      };
    }

    return {
      mostPopularResult: [],
      searchCountryResults: availableCountries.filter((country) =>
        country.label
          .toLowerCase()
          .replace(/\s+/g, '')
          .includes(query.toLowerCase().replace(/\s+/g, ''))
      )
    };
  }, [roamingCountries, query]);

  const availableRegions = roamingRegions
    ? Object.keys(roamingRegions).map((region) => ({
        value: region,
        label: region
      }))
    : [];

  const handleCountrySelect = (option: RoamingCountry) => {
    onSelect(option, false);
    setIsOptionDropdownOpen(false);
    setQuery('');
  };

  const handleRegionSelect = (option: RoamingCountry) => {
    onSelect(option, true);
    setIsOptionDropdownOpen(false);
    setQuery('');
  };

  const onTabChange = (index: number) => {
    setSelectedTab(index);
    setIsOptionDropdownOpen(true);
    setQuery('');
    onSelect(undefined as unknown as RoamingCountry, false);
  };

  if (!roamingCountries) {
    return <Loader />;
  }

  return (
    <Tab.Group onChange={onTabChange} selectedIndex={selectedTab}>
      <Tab.List className="flex relative text-sm bg-[#EBEBEB] rounded-xl w-fit">
        <TabTitle last={!withRegions}>Country</TabTitle>
        {withRegions && <TabTitle last>Region</TabTitle>}
      </Tab.List>

      <Tab.Panels
        className={twMerge(
          'py-2 px-2 bg-white rounded-b-xl text-base rounded-tr-xl max-h-[70%] md:max-h-[77%] overflow-y-auto'
        )}
      >
        <Tab.Panel>
          <Combobox value={selectedOption} onChange={handleCountrySelect}>
            <div className="relative mt-1">
              <div className="relative mb-2 flex gap-4 items-center">
                {!selectedOption && (
                  <Combobox.Button
                    className="absolute inset-y-0 left-2 flex items-center pl-2"
                    onClick={() => setIsOptionDropdownOpen(true)}
                  >
                    <span className="bg-[#B9B9B9] rounded-full p-1 text-white">
                      <SearchIcon className="w-3.5 h-3.5" />
                    </span>
                  </Combobox.Button>
                )}
                <Combobox.Input
                  className={twMerge(
                    'w-full border-none p-3 font-semibold rounded-lg focus:bg-[#F3F3F3] focus:outline-none',
                    !selectedOption && 'bg-[#F3F3F3] pl-12'
                  )}
                  displayValue={(country: RoamingCountry) => country.label}
                  onChange={(event) => setQuery(event.target.value)}
                  onClick={() => setIsOptionDropdownOpen(true)}
                  placeholder="Type in country name"
                />
                {selectedOption && SelectedCountryIcon && (
                  <div className="min-w-6 mr-3">
                    <CountryIcon
                      Icon={SelectedCountryIcon}
                      title={selectedOption.value}
                    />
                  </div>
                )}
              </div>
              <Transition
                as={Fragment}
                leave="transition ease-in duration-100"
                leaveFrom="opacity-100"
                leaveTo="opacity-0"
                show={isOptionDropdownOpen}
              >
                <Combobox.Options className="bg-white -left-2 px-2 pb-2 h-fit overflow-auto rounded-b-xl">
                  {searchCountryResults.length === 0 && query !== '' ? (
                    <div className="relative cursor-default select-none p-4 pt-6 border-t">
                      Nothing found.
                    </div>
                  ) : (
                    <div>
                      {mostPopularResult.length > 0 && (
                        <>
                          <p className="sticky top-0 bg-white z-10 text-center text-[#B9B9B9] text-xs py-1">
                            Most popular
                          </p>
                          <div className="divide-y divide-slate-100">
                            {mostPopularResult.map(
                              (country: RoamingCountry) => (
                                <CountryOption
                                  key={country.value}
                                  country={country}
                                  Icon={
                                    countriesIcons[
                                      country.value as keyof typeof countriesIcons
                                    ]
                                  }
                                />
                              )
                            )}
                          </div>
                        </>
                      )}
                      {!query && (
                        <p className="sticky top-0 bg-white z-10 mt-2 text-center text-[#B9B9B9] text-xs py-1">
                          All countries
                        </p>
                      )}
                      <div className="divide-y divide-slate-100">
                        {searchCountryResults.map((country: RoamingCountry) => (
                          <CountryOption
                            key={country.value}
                            country={country}
                            Icon={
                              countriesIcons[
                                country.value as keyof typeof countriesIcons
                              ]
                            }
                          />
                        ))}
                      </div>
                    </div>
                  )}
                </Combobox.Options>
              </Transition>
            </div>
          </Combobox>
        </Tab.Panel>
        <Tab.Panel>
          <Combobox
            value={selectedOption}
            onChange={handleRegionSelect}
            by={(value: RoamingRegion, option: RoamingRegion) =>
              value?.value === option.value
            }
          >
            <div className="relative mt-1">
              <Transition
                as={Fragment}
                leave="transition ease-in duration-100"
                leaveFrom="opacity-100"
                leaveTo="opacity-0"
                show
              >
                <Combobox.Options className="bg-white -left-2 px-2 pb-2 absolute w-[calc(100%_+_16px)] overflow-auto rounded-b-xl">
                  <p className="mt-2 text-center text-[#B9B9B9] text-xs">
                    All regions
                  </p>
                  <div className="divide-y divide-slate-100">
                    {availableRegions.map((region: RoamingRegion) => (
                      <CountryOption key={region.value} country={region} />
                    ))}
                  </div>
                </Combobox.Options>
              </Transition>
            </div>
          </Combobox>
        </Tab.Panel>
      </Tab.Panels>
    </Tab.Group>
  );
}
