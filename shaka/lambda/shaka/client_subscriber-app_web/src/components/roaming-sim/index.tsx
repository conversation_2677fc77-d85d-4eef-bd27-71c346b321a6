import { useNavigate } from '@tanstack/react-router';
import { useEffect, useState } from 'react';
import FullPageLoader from '../common/FullPageLoader';
import { ROUTES } from 'src/config/routes';
import withDrawer from 'src/hocs/withDrawer';
import CountrySelection from './CountrySelection';
import useRoamingSim from 'src/hooks/useRoamingSim';
import OfferOption from './OfferOption';
import ProgressButtons from '../common/ProgressButtons';
import RoamingSimPayment from './Payment';
import { countries } from 'src/config/countries';
import {
  RoamingCountry,
  RoamingEsimStep,
  RoamingRegion
} from 'src/types/roaming-sim';
import { getRoamingZone, setRoamingZone } from 'src/config/localStorageActions';
import EsimSettings from '../EsimSettings';
import SuccessStep from './SuccessStep';
import useSubscription from 'src/hooks/useSubscription';
import { defaultRoamingSimName, userActions } from 'src/helpers';

const RoamingSimContent = () => {
  const searchParams = new URLSearchParams(window.location.search);
  const travelSimStep = searchParams.get('travel-esim');
  const userAction = searchParams.get('action');
  const preSelectedZone = getRoamingZone();
  const {
    fetchSubscriber,
    subscriber,
    subscriberPlanSimList,
    currentPlanIndex,
    currentPlan
  } = useSubscription();

  const currentlySelectedRoamingSimSubscriptionId =
    subscriberPlanSimList?.[currentPlanIndex]?.id;

  const navigate = useNavigate();

  const {
    roamingSimOffers,
    isLoading,
    buyRoamingSim,
    roamingCountries,
    roamingRegions
  } = useRoamingSim();

  const hasPlans = !!subscriber?.plans?.length;
  const roamingEsims = subscriber?.roaming_esims ?? [];
  const roamingEsimsCount = roamingEsims.length;
  const travelSimPlanIndex = hasPlans
    ? currentPlanIndex - subscriber.plans.length
    : 0;

  const selectedRoamingESim = roamingEsims[travelSimPlanIndex];

  const customLabelName = selectedRoamingESim?.custom_name;
  const isAddingDataOrCountry =
    userAction === userActions.moreData ||
    userAction === userActions.newCountry;

  const [step, setStep] = useState(
    travelSimStep ? parseInt(travelSimStep) - 1 : RoamingEsimStep.SELECT_COUNTRY
  );
  let nextRoamingSimNumber = roamingEsimsCount > 0 ? roamingEsimsCount + 1 : 1;
  if (step === RoamingEsimStep.SUCCESS || step === RoamingEsimStep.SETTINGS) {
    nextRoamingSimNumber -= 1;
  }
  let roamingSimName;

  if (customLabelName && isAddingDataOrCountry) {
    roamingSimName = selectedRoamingESim?.custom_name;
  } else if (isAddingDataOrCountry) {
    roamingSimName = `${defaultRoamingSimName} ${travelSimPlanIndex + 1}`;
  } else {
    roamingSimName = `${defaultRoamingSimName} ${nextRoamingSimNumber}`;
  }

  const [availableOffers, setAvailableOffers] = useState<(number | string)[]>();

  const [selectedRoamingOffer, setSelectedRoamingOffer] = useState<
    null | number
  >(null);

  const regions = Object.keys(roamingRegions || {}).map((region) => ({
    value: region,
    label: region
  }));

  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [selectedZone, setSelectedZone] = useState<RoamingRegion | undefined>(
    countries.find((country) => country.value === preSelectedZone) ||
      regions.find((region) => region.value === preSelectedZone)
  );

  const isZoneRegion = regions.some(
    (region) => region.value === selectedZone?.value
  );

  const isNextButtonDisabled =
    (step === RoamingEsimStep.SELECT_COUNTRY && selectedZone === undefined) ||
    (step === RoamingEsimStep.SELECT_OFFER && !selectedRoamingOffer);

  const handleZoneSelect = (zone: RoamingCountry, isRegion: boolean) => {
    if (!zone) {
      setSelectedZone(undefined);
      return;
    }

    let offersIds: (number | string)[] = [];

    if (isRegion && roamingRegions) {
      offersIds = roamingRegions[zone.value];
    } else if (roamingCountries) {
      offersIds = roamingCountries[zone.value];
    }

    setAvailableOffers(offersIds.sort((a, b) => Number(a) - Number(b)));
    setSelectedZone(zone);
    goNextStep();
  };

  const handleConfirmAndPay = (isNewPaymentMethod: boolean) => {
    if (!selectedRoamingOffer) return;

    buyRoamingSim({
      id: selectedRoamingOffer,
      isNew: isNewPaymentMethod,
      country_code: selectedZone?.value,
      extend_subscription_id:
        userAction === userActions.getTravelEsim
          ? null
          : currentlySelectedRoamingSimSubscriptionId
    }).then(({ client_secret }) => {
      if (client_secret) {
        setClientSecret(client_secret);
        setRoamingZone(selectedZone!.label);
        return;
      }

      navigate({ to: ROUTES.Settings, search: { modal: 'bolt-ons-payment' } });
    });
  };

  const returnToMainScreen = () => {
    setClientSecret(null);
  };

  const goNextStep = () => {
    setStep((currentStep) => currentStep + 1);

    if (step === RoamingEsimStep.SELECT_OFFER && !clientSecret) {
      handleConfirmAndPay(false);
      return;
    }

    if (step === RoamingEsimStep.SETTINGS) {
      // to update subscriber data after payment
      fetchSubscriber();
    }
  };

  const goPreviousStep = () => {
    setSelectedRoamingOffer(null);
    setStep((currentStep) => currentStep - 1);
  };

  useEffect(() => {
    if (preSelectedZone && roamingSimOffers && !availableOffers) {
      const preSelectedRegion = regions.find(
        (region) => region.value === preSelectedZone
      );
      setAvailableOffers(
        preSelectedRegion
          ? roamingRegions?.[preSelectedRegion.label]
          : roamingCountries?.[preSelectedZone]
      );
    }
  }, [
    availableOffers,
    preSelectedZone,
    regions,
    roamingCountries,
    roamingRegions,
    roamingSimOffers
  ]);

  const sortedOffers =
    (roamingSimOffers &&
      availableOffers?.sort(
        (a, b) =>
          Number(roamingSimOffers[a].data) - Number(roamingSimOffers[b].data)
      )) ||
    [];

  const isLastSteps =
    step === RoamingEsimStep.SUCCESS || step === RoamingEsimStep.SETTINGS;

  // console.log('selected', selectedRoamingESim, 'current', currentPlan, 'index', currentPlanIndex);

  if (isLoading) {
    return <FullPageLoader />;
  }

  return (
    <div className="-mb-5">
      <div className="text-center pb-6 pt-0.5 sticky top-0 z-20 bg-general">
        <span className="bg-white rounded-full text-xs py-1 px-8">
          {roamingSimName}
        </span>
      </div>

      {clientSecret && step === RoamingEsimStep.PAYMENT ? (
        <div className="h-[75dvh] relative overflow-auto">
          <RoamingSimPayment
            onReturn={returnToMainScreen}
            clientSecret={clientSecret}
          />
        </div>
      ) : (
        <div className="px-5 h-[75dvh] text-black relative overflow-auto">
          {step === RoamingEsimStep.SELECT_COUNTRY && (
            <>
              <h4 className="font-semibold pb-4 sticky top-0 z-20 bg-general">
                Choose your destination
              </h4>
              <CountrySelection
                onSelect={handleZoneSelect}
                selectedZone={selectedZone}
                isRegion={isZoneRegion}
              />
            </>
          )}
          {step === RoamingEsimStep.SELECT_OFFER && (
            <>
              <h4 className="font-semibold pb-4 sticky top-0 z-20 bg-general">
                Choose your package
              </h4>
              <div className="space-y-3 pb-20">
                {availableOffers &&
                  roamingSimOffers &&
                  sortedOffers.map((id: string | number) => (
                    <OfferOption
                      selected={
                        selectedRoamingOffer === parseInt(id.toString())
                      }
                      offer={roamingSimOffers[id]}
                      onClick={() =>
                        setSelectedRoamingOffer(parseInt(id.toString()))
                      }
                      selectedCountry={selectedZone}
                      key={id}
                    />
                  ))}
              </div>
            </>
          )}
          {step === RoamingEsimStep.SETTINGS && (
            <EsimSettings
              onNextClick={goNextStep}
              // HACK: Both props are required due to EsimSettings component design:
              // - onNextClick: used by ProgressLinks for stepper navigation
              // - onNext: passed to EsimSettingsContent for eSIM sharing functionality
              // TODO: Consider refactoring EsimSettings to use consistent
              esimId={selectedRoamingESim?.id || currentPlan?.id}
              onShareEsim={goNextStep}
              hideBackButton
            />
          )}
          {step === RoamingEsimStep.SUCCESS && <SuccessStep />}
        </div>
      )}
      {!isLastSteps && (
        <div className="fixed sm:absolute inset-x-5 sm:bottom-4 bottom-6">
          <ProgressButtons
            onNext={goNextStep}
            onBack={goPreviousStep}
            hideBackButton={step === RoamingEsimStep.SELECT_COUNTRY}
            hideNextButton={step === RoamingEsimStep.PAYMENT}
            disabledNext={isNextButtonDisabled}
          />
        </div>
      )}
    </div>
  );
};

const RoamingSimDrawer = withDrawer(RoamingSimContent);

export default RoamingSimDrawer;
