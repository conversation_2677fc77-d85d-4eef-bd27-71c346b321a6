import { Popover } from '@headlessui/react';
import { useState } from 'react';
import useSubscription from 'src/hooks/useSubscription';
import { twMerge } from 'tailwind-merge';
import Button from './common/Button';
import { useRequest } from 'src/hooks/useRequest';
import { updateSubscriber } from 'src/api/data';
import ErrorText from './common/ErrorText';
import { PenIcon } from 'src/assets/icons/Pen';
import { defaultSimName } from 'src/helpers';

export default function SubscriptionCustomName({
  planName,
  withPlanSelection,
  withPlanNameUpdate,
  travelSim
}: {
  planName: string;
  withPlanSelection?: boolean;
  withPlanNameUpdate?: boolean;
  travelSim?: boolean;
}) {
  const {
    subscriber,
    currentPlanIndex,
    setCurrentPlanIndex,
    subscriberPlanSimList,
    setSubscriber
  } = useSubscription();
  const { run: runUpdateSubscriber, error } = useRequest(updateSubscriber, {
    defaultErrorText: 'Entered name is too long. Maximum 20 characters.'
  });

  const [customName, setCustomName] = useState(planName);

  return (
    <Popover className="relative z-50">
      {({ open }) => (
        <>
          {open && withPlanSelection && (
            <div className="background fixed bg-black/30 w-screen h-screen left-0 top-0" />
          )}

          <Popover.Button
            className={twMerge(
              'rounded-full bg-white px-4 py-1 text-xs focus:outline-none relative',
              withPlanNameUpdate && 'pr-7'
            )}
          >
            {planName}
            {withPlanNameUpdate && (
              <PenIcon className="absolute inset-y-0 m-auto right-2.5 size-2" />
            )}
          </Popover.Button>

          {(withPlanSelection || withPlanNameUpdate) && (
            <Popover.Panel className="absolute z-100 left-1/2 -translate-x-1/2 top-[32px]">
              {({ close }) => (
                <div className="flex flex-col gap-2 p-4 bg-white rounded-3xl min-w-[260px] shadow-2xl">
                  {withPlanSelection && (
                    <>
                      {subscriber?.plans.map((plan, index) => (
                        <button
                          key={plan.plan_id}
                          onClick={() => {
                            setCurrentPlanIndex(index);
                            close();
                          }}
                          className={twMerge(
                            'text-sm text-left bg-[#F3F3F3] px-3.5 py-3.5 rounded-2xl border',
                            currentPlanIndex === index
                              ? 'border-black'
                              : 'border-transparent'
                          )}
                        >
                          {plan.user_subscription_name ||
                            `${defaultSimName} ${index + 1}`}
                        </button>
                      ))}
                    </>
                  )}
                  {withPlanNameUpdate && (
                    <>
                      <h4 className="tex-sm font-semibold mb-2">Label plan</h4>
                      <input
                        className="plan-name-input text-sm text-left bg-[#F3F3F3] px-3.5 py-3.5 rounded-2xl mb-3"
                        value={customName}
                        onChange={(e) => setCustomName(e.target.value)}
                      />

                      {/* <div> */}
                      <ErrorText size="small">{error}</ErrorText>
                      <Button
                        color="black"
                        size="small"
                        fullWidth
                        onClick={() => {
                          runUpdateSubscriber({
                            ...(travelSim
                              ? { roaming_sim_name: customName }
                              : { plan_name: customName }),
                            subscription_id:
                              subscriberPlanSimList[currentPlanIndex].id
                          }).then((res) => {
                            setSubscriber(res);
                            close();
                          });
                        }}
                      >
                        Confirm
                      </Button>
                      {/* </div> */}
                    </>
                  )}
                </div>
              )}
            </Popover.Panel>
          )}
        </>
      )}
    </Popover>
  );
}
