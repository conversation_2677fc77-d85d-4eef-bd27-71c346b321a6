import {
  ChangeArrowsIcon,
  CoinIcon,
  LockIcon,
  LogoutIcon
} from 'src/assets/icons';
import { TermsIcon } from 'src/assets/icons/Terms';
import { LinkProps } from '@tanstack/react-router';
import { SecurityIcon } from 'src/assets/icons/Security';
import { EsimIcon } from 'src/assets/icons/Esim';
import { isOpenId } from 'src/config/env-vars';
import { ROUTES } from 'src/config/routes';
import SettingLink, { settingsLinkStyles } from './SettingsLink';
import useSubscription from 'src/hooks/useSubscription';

export default function OtherLinksBlock({
  onLogout
}: {
  onLogout: () => void;
}) {
  const { hasEsim } = useSubscription();

  const otherLinks: {
    to: LinkProps['to'];
    label: string;
    Icon: (props: React.SVGProps<SVGSVGElement>) => JSX.Element;
    blank?: boolean;
    disabled?: boolean;
  }[] = [
    {
      to: ROUTES.Terms,
      label: 'Terms and conditions',
      Icon: TermsIcon
    },
    {
      to: ROUTES.Policy,
      label: 'Privacy policy',
      Icon: SecurityIcon
    },
    ...(!isOpenId
      ? [
          {
            to: ROUTES.ChangePassword,
            label: 'Change password',
            Icon: LockIcon
          }
        ]
      : []),
    {
      to: ROUTES.PortNumber,
      label: 'Number porting',
      Icon: ChangeArrowsIcon
    },
    {
      to: ROUTES.EsimSettingInternal,
      label: 'eSIM instructions',
      Icon: EsimIcon,
      disabled: !hasEsim
    },
    {
      to: ROUTES.Dashboard,
      label: 'Bill overview',
      disabled: true,
      Icon: CoinIcon
    }
  ] as const;

  return (
    <div className="w-full flex flex-col bg-white rounded-xl divide-y divide-black/5">
      {otherLinks.map(({ to, label, disabled, Icon, blank }) => (
        <SettingLink to={to} key={label} disabled={disabled} blank={blank}>
          <Icon className="h-4 w-4" />
          {label}
        </SettingLink>
      ))}
      <button onClick={onLogout} className={settingsLinkStyles}>
        <LogoutIcon className="h-4 w-4" />
        Sign out
      </button>
    </div>
  );
}
