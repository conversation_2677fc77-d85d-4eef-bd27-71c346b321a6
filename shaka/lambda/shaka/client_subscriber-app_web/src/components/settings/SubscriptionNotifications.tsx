import { humanReadableDateFormat } from 'src/helpers';
import { InfoIcon } from 'src/assets/icons/Info';
import BigActionButton from '../common/BigActionButton';
import NotificationDialog from '../common/NotificationDialog';
import { Fragment, useState } from 'react';
import { cancelSubscriptionUpdate } from 'src/api/subscription';
import useSubscription from 'src/hooks/useSubscription';
import { PlanChangeType } from 'src/types/subscriber';
import { Transition } from '@headlessui/react';

export default function SubscriptionNotifications() {
  const { fetchSubscriber, currentPlan } = useSubscription();

  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const {
    can_cancel_change: canCancelPlanChange,
    next_bill_date_epoch: billDate,
    latest_plan_change: latestPlanChange
  } = currentPlan;

  const isPlanChanged =
    latestPlanChange &&
    latestPlanChange.change_type !== PlanChangeType.CANCELLATION;

  const isPlanCancelled =
    latestPlanChange &&
    latestPlanChange.change_type === PlanChangeType.CANCELLATION;

  const closeDialog = () => {
    setIsDialogOpen(false);
  };

  const cancelChange = () => {
    cancelSubscriptionUpdate(latestPlanChange!.id, currentPlan.id).then(() => {
      fetchSubscriber();
      closeDialog();
    });
  };

  const handleCancelPlanChange = () => {
    if (latestPlanChange) {
      cancelChange();
    }
  };

  const handleCancelPlanCancelation = () => {
    if (
      latestPlanChange &&
      latestPlanChange.change_type === PlanChangeType.CANCELLATION
    ) {
      cancelChange();
    }
  };

  return (
    <>
      <Transition
        as={Fragment}
        show={Boolean(billDate && isPlanCancelled && canCancelPlanChange)}
        enter="transform transition duration-300"
        enterFrom="opacity-0 scale-50"
        enterTo="opacity-100 scale-100"
      >
        <div className="flex gap-2 mt-5">
          <div className="grow bg-white rounded-2xl py-3 px-5 font-semibold text-xs flex items-center gap-3">
            <InfoIcon />
            <div className="flex flex-col justify-center gap-0.5">
              <p>Your plan is due to terminate</p>
              <p>on the {humanReadableDateFormat(new Date(billDate * 1000))}</p>
            </div>
          </div>

          <BigActionButton
            actionType="cancel"
            cancelText="Cancel"
            onClick={() => setIsDialogOpen(true)}
          />

          <NotificationDialog
            isOpen={isDialogOpen}
            onCancel={handleCancelPlanCancelation}
            onSubmit={closeDialog}
            cancelButtonText="Yes"
            submitButtonText="No"
            title="Stop plan termination?"
            description="If you stop the termination, you will continue on your previous plan."
          />
        </div>
      </Transition>

      <Transition
        as={Fragment}
        show={Boolean(billDate && isPlanChanged && canCancelPlanChange)}
        enter="transform transition duration-300"
        enterFrom="opacity-0 scale-50"
        enterTo="opacity-100 scale-100"
      >
        <div className="flex gap-2 mt-5 mb-6">
          <div className="grow bg-white rounded-2xl py-3 px-5 font-semibold text-xs flex items-center gap-3">
            <InfoIcon />
            <div className="flex flex-col justify-center gap-0.5">
              <p>Your plan is changing</p>
              <p>on the {humanReadableDateFormat(new Date(billDate * 1000))}</p>
            </div>
          </div>

          <BigActionButton
            actionType="cancel"
            cancelText="Cancel"
            onClick={() => setIsDialogOpen(true)}
          />

          <NotificationDialog
            isOpen={isDialogOpen}
            onCancel={closeDialog}
            onSubmit={handleCancelPlanChange}
            title="Are you sure you want to cancel this plan change?"
            description="In case of cancellation, you will continue on your previous plan."
          />
        </div>
      </Transition>
    </>
  );
}
