import { useClient } from 'src/hooks/useClient';
import { createPortal } from 'react-dom';
import { Dialog, Transition } from '@headlessui/react';
import CloseButton from 'components/common/CloseButton.tsx';

type DrawerProps = {
  title: string;
  isOpen: boolean;
  onClose: () => void;
  disableClose?: boolean;
  withOverflow?: boolean;
  minHeightMobile?: string;
};

const VideoDrawer = ({ title, isOpen, onClose }: DrawerProps) => {
  const { clientName } = useClient();

  return (
    <>
      {createPortal(
        <Transition show={isOpen}>
          <Dialog className="relative z-10" onClose={onClose}>
            <Transition.Child
              enter="ease-in-out duration-100"
              enterFrom="opacity-0"
              enterTo="opacity-100"
              leave="ease-in-out duration-100"
              leaveFrom="opacity-100"
              leaveTo="opacity-0"
            >
              <div className="transition-all fixed inset-0 bg-gray-300/75 backdrop-blur-sm" />
            </Transition.Child>

            <Dialog.Panel className="fixed inset-0 top-10 flex items-end">
              <Transition.Child
                enter="transform transition ease-in-out duration-500 sm:duration-700"
                enterFrom="translate-y-full"
                enterTo="translate-y-0"
                leave="transform transition ease-in-out duration-500 sm:duration-700"
                leaveFrom="translate-y-0"
                leaveTo="translate-y-full"
                className="pointer-events-auto relative w-full bg-[#F3F3F3] pt-6 shadow-xl rounded-t-3xl min-h-[50%] max-h-[calc(100dvh_-_40px)] h-auto overflow-hidden"
              >
                <div className="relative">
                  {title && (
                    <Dialog.Title className="text-center text-base font-semibold leading-6 text-gray-900 uppercase">
                      {title}
                    </Dialog.Title>
                  )}

                  <div className="absolute top-1/2 -translate-y-1/2 right-0 mr-4">
                    <CloseButton onClose={onClose} />
                  </div>
                </div>
                <div className="flex flex-col h-full px-4">
                  <p className="my-4 mb-6">
                    Follow the instructions below to install your eSIM. Label
                    your new plan as "{clientName}"
                  </p>
                  <div className="flex-grow flex items-end justify-center mt-auto">
                    <video
                      autoPlay
                      loop
                      muted
                      playsInline
                      controls={false}
                      src="/apple_installation_esim.mp4"
                      className="w-full object-contain"
                      style={{ maxHeight: 'calc(100vh - 180px)' }}
                    >
                      Your browser does not support the video tag.
                    </video>
                  </div>
                </div>
              </Transition.Child>
            </Dialog.Panel>
          </Dialog>
        </Transition>,
        document.body
      )}
    </>
  );
};

export default VideoDrawer;
