import { FingerTapIcon } from 'src/assets/icons/FingerTap';
import WhiteBlock from 'src/components/common/WhiteBlock';

export default function AndroidThirdStep({
  label = 'Step 3',
  labelColor
}: {
  label?: string;
  labelColor?: string;
}) {
  return (
    <WhiteBlock
      title="Check your settings"
      label={label}
      labelColor={labelColor}
    >
      <div className="text-sm my-2">
        <p className="font-semibold">Make sure data roaming is on:</p>
        <p className="italic">
          Network & internet &gt; SIMs &gt; Pick this SIM &gt; Access point
          names
        </p>
      </div>
      <div className="text-sm my-3">
        <p className="font-semibold">Check your APN settings:</p>
        <p className="italic">
          Network & internet &gt; SIMs &gt; Pick this SIM &gt; Access point
          names
        </p>
      </div>
      <div className="mt-3">
        <div className="h-[100px] m-auto">
          <img
            src="/apn-setup1.png"
            className="w-full h-full object-contain"
            alt="APN setup"
          />
        </div>
        <div className="text-xs flex gap-2 items-center justify-center text-[#1EC25F] mt-1">
          <FingerTapIcon />
          <span className="font-semibold">Tap to change</span>
        </div>

        <p className="text-sm mt-4 font-bold">
          Make sure your Mobile Data APN is set to “globaldata”
        </p>
      </div>
    </WhiteBlock>
  );
}
