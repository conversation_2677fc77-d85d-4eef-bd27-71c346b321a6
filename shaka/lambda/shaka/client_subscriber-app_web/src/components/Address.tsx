import { useEffect, useState } from 'react';
import { updateSubscriber } from 'src/api/data';
import ProgressLinks, {
  ProgressLinksProps
} from 'src/components/common/ProgressLinks';
import useSubscription from 'src/hooks/useSubscription';
import {
  getAddressAutocompleteByPostCode,
  getAddressDetailsByUrl
} from 'src/api/address';
import WhiteBlock from 'src/components/common/WhiteBlock';
import { SearchIcon } from 'src/assets/icons/Search';
import ErrorText from 'src/components/common/ErrorText';
import { LocationIcon } from 'src/assets/icons/Location';
import { useForm } from 'react-hook-form';
import { AddressInput } from 'src/schemas/address';
import Loader from 'src/components/common/Loader';

type LocationSuggestion = {
  address: string;
  id: string;
  url: string;
};

export default function AddressDetails({
  onSubmitSuccess,
  ...progressNavProps
}: {
  onSubmitSuccess: () => void;
} & ProgressLinksProps) {
  const { fetchSubscriber, subscriber, currentPlan } = useSubscription();

  const { watch, reset, handleSubmit, setValue } = useForm<AddressInput>();
  const watchAllFields = watch();

  const [locationSuggestions, setLocationSuggestions] = useState<
    LocationSuggestion[]
  >([]);
  const [isEmptyResponse, setIsEmptyResponse] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const showResultBlock =
    locationSuggestions?.length > 0 || isEmptyResponse || isLoading;

  const onFormSubmit = (data: AddressInput) => {
    updateSubscriber({
      subscription_id: currentPlan.id,
      address: [
        data?.line_1,
        data?.line_2,
        data?.line_3,
        data?.town_or_city,
        data?.country,
        data?.postcode
      ]
        .filter(Boolean)
        .join('\n')
    })
      .then(() => {
        fetchSubscriber();
        onSubmitSuccess();
      })
      .catch((error) => {
        setError(error.response?.data.error || 'Something went wrong');
      });
  };

  const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const postcode = e.target.value;
    setIsEmptyResponse(false);
    setValue('input', postcode);

    if (postcode.length > 3) {
      setIsLoading(true);
      getAddressAutocompleteByPostCode(e.target.value).then((data) => {
        setLocationSuggestions(data.suggestions);
        setIsEmptyResponse(data.suggestions.length === 0);
        setIsLoading(false);
      });
    }
  };

  const getAddressDetails = (url: string) => () => {
    setValue('input', '');
    getAddressDetailsByUrl(url).then((data) => {
      reset(data);
      setLocationSuggestions([]);
    });
  };

  useEffect(() => {
    if (subscriber?.address && !watchAllFields.postcode) {
      const address = subscriber.address.split('\n');
      const postCodeIndex = address.length - 1;
      const countryIndex = address.length - 2;
      reset({
        line_1: address[0],
        line_2: address[1],
        line_3: address[2],
        town_or_city: address[3],
        country: address[countryIndex],
        postcode: address[postCodeIndex]
      });
    }
  }, [subscriber]);

  return (
    <form onSubmit={handleSubmit(onFormSubmit)}>
      <div className="bg-white rounded-xl p-5 w-full relative mb-6">
        <div className="flex gap-5 items-center">
          <SearchIcon />
          <input
            value={watchAllFields.input}
            type="text"
            placeholder={
              watchAllFields.postcode ? 'New address search' : 'Postcode'
            }
            className="address-input"
            onChange={onChange}
          />
        </div>

        {showResultBlock && (
          <div className="mt-4 border-t pt-3">
            {isEmptyResponse && (
              <div className="my-2 text-sm text-center">
                No address found for this postcode. Please try another one.
              </div>
            )}
            {isLoading && (
              <div className="flex justify-center my-4">
                <Loader />
              </div>
            )}

            {locationSuggestions.map((location) => (
              <div
                key={location.id}
                className="flex items-start py-2 gap-2 hover:bg-[#f7f7f7] cursor-pointer rounded-lg"
                onClick={getAddressDetails(location.url)}
              >
                <div className="mt-[1px]">
                  <LocationIcon />
                </div>
                <p className="text-sm text-left">{location.address}</p>
              </div>
            ))}
          </div>
        )}
      </div>

      {watchAllFields.postcode && (
        <>
          <WhiteBlock title="Selected address">
            <div className="flex flex-col">
              <p className="font-medium text-sm">
                {[watchAllFields.line_2, watchAllFields.line_1]
                  .filter(Boolean)
                  .join(', ')}
              </p>
              <p className="text-xs text-gray-400">
                {[watchAllFields.line_3, watchAllFields.town_or_city]
                  .filter(Boolean)
                  .join(', ')}
              </p>
            </div>
          </WhiteBlock>
        </>
      )}

      <div className="mt-4">
        <ErrorText>{error}</ErrorText>
      </div>

      <ProgressLinks
        disabledNext={!watchAllFields.postcode}
        {...progressNavProps}
      />
    </form>
  );
}
