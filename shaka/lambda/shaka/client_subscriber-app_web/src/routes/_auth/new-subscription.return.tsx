import { Navigate } from '@tanstack/react-router';
import { createFileRoute } from '@tanstack/react-router';
import { Step } from 'src/components/SubscriptionSwiper/NewSubscriptionDrawer';
import { ROUTES } from 'src/config/routes';
import useSubscription from 'src/hooks/useSubscription';
import PageTitle from 'src/components/common/PageTitle';
import Loader from 'src/components/common/Loader';
import { setCorrelationId } from 'src/config/localStorageActions.ts';

export const Route = createFileRoute('/_auth/new-subscription/return')({
  component: NewSubscriptionReturn
});

export function NewSubscriptionReturn() {
  const { subscriber } = useSubscription();

  const correlationId = new URLSearchParams(window.location.search).get(
    'correlation_id'
  );

  const planWithMatchingCorrelationId = subscriber?.plans?.find(
    (plan) => plan.correlation_id === correlationId
  );

  if (!planWithMatchingCorrelationId) {
    setCorrelationId(correlationId!);
    return (
      <div className="h-screen grow flex flex-col justify-center items-center">
        <PageTitle>Transaction in progress</PageTitle>
        <p className="text-center mb-20">
          Your payment is being processed.
          <br />
          Please wait a moment
        </p>
        <Loader />
      </div>
    );
  }

  return (
    <Navigate
      to={ROUTES.Settings}
      search={{ 'new-subscription': Step.SelectSimType }}
      replace
    />
  );
}
