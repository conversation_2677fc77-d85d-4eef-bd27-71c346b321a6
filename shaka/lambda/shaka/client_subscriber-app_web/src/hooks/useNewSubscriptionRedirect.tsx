import { useNavigate } from '@tanstack/react-router';
import { useEffect, useState } from 'react';
import { getNewSubscriptionStep } from 'src/config/localStorageActions';
import { ROUTES } from 'src/config/routes';

export default function useNewSubscriptionRedirect() {
  const [newSubscriptionState] = useState(getNewSubscriptionStep());
  const navigate = useNavigate();

  useEffect(() => {
    if (newSubscriptionState) {
      navigate({
        to: ROUTES.Settings,
        search: { 'new-subscription': Number(newSubscriptionState) }
      });
    }
  }, [newSubscriptionState]);
}
