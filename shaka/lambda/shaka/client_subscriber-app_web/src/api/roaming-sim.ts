import api from '.';
import { StripeResponse } from 'src/types/stripe';
import { RoamingSimList } from 'src/types/roaming-sim';

const fetchRoamingSimOffers = (): Promise<RoamingSimList> =>
  api.get('/data/roaming-sims/').then((res) => res.data);

const buyRoamingSim = (payload: {
  id: number;
  isNew: boolean;
  country_code?: string;
  extend_subscription_id?: number | null;
}): Promise<StripeResponse> =>
  api.post('/roaming-sim/buy/', payload).then((res) => res.data);

export { fetchRoamingSimOffers, buyRoamingSim };
