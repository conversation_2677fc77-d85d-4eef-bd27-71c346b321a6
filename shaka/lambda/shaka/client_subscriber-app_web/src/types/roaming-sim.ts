export enum RoamingEsimStep {
  SELECT_COUNTRY,
  SELECT_OFFER,
  PAYMENT,
  SETTINGS,
  SUCCESS
}

export interface RoamingSim {
  status: 'active' | 'inactive' | 'expired';
  zone: string;
  expires: string;
  data: {
    used: number;
    left: number;
  } | null;
  id: number;
  custom_name: string;
  is_unlimited: boolean;
  esim_status: string;
  correlation_id: string;
  show_esim_settings: boolean;
}

export interface RoamingSimOffer {
  data: string;
  price: number;
  days: number;
}

export interface RoamingSimList {
  packages: Record<string, RoamingSimOffer>;
  countries: Record<string, number[]>;
  regions: Record<string, number[]>;
  most_popular: string[];
}

export type RoamingCountry = {
  label: string;
  zone?: string;
  value: string;
};

export type RoamingRegion = {
  label: string;
  value: string;
};
