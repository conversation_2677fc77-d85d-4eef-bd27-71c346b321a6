export function PenIcon({ ...props }: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      viewBox="0 0 17 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clip-path="url(#clip0_1783_4948)">
        <path
          d="M13.0111 -0.000976562C12.7685 -0.000976562 12.5283 0.0475013 12.3046 0.14161C12.0824 0.235135 11.8809 0.371851 11.7119 0.543816L1.70889 10.4983C1.63462 10.5722 1.58074 10.6641 1.5525 10.7649L0.0225006 16.2293C-0.0366413 16.4404 0.0227425 16.6671 0.177842 16.8223C0.332943 16.9774 0.55964 17.0368 0.770861 16.9776L6.23515 15.4476C6.33603 15.4193 6.42789 15.3654 6.5018 15.2912L16.4561 5.2883L16.4578 5.28666C16.6277 5.11767 16.7626 4.9168 16.8548 4.69556C16.9473 4.47361 16.9949 4.23554 16.9949 3.99509C16.9949 3.75464 16.9473 3.51656 16.8548 3.2946C16.7626 3.07336 16.6277 2.87248 16.4578 2.7035L16.4561 2.70187L14.3115 0.545085C14.1423 0.372523 13.9404 0.235364 13.7175 0.14161C13.4939 0.0475013 13.2537 -0.000976562 13.0111 -0.000976562Z"
          fill="black"
        />
      </g>
      <defs>
        <clipPath id="clip0_1783_4948">
          <rect width="17" height="17" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}
