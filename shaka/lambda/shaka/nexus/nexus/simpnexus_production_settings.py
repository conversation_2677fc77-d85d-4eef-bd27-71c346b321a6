from .settings import *  # pylint: disable=wildcard-import, unused-wildcard-import

DEBUG = False

ALLOWED_HOSTS = ['*']  # WAF does host matching, this is mostly because the EB health check hits only the IP
SITE_URL = 'https://simpnexus.shaka.tel'
CSRF_TRUSTED_ORIGINS = [SITE_URL]

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': '/var/log/django',
            'formatter': 'simple',
        },
    },
    'formatters': {
        'simple': {
            'format': '%(levelname)s %(asctime)s %(name)s.%(funcName)s:%(lineno)s- %(message)s'
        },
    },
    'loggers': {
        'django': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': True,
        },
        '': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': False,
        }
    },
}


DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": "simpnexus",
        "HOST": os.environ.get('DB_HOST', 'localhost'),
        "USER": os.environ.get('DB_USER', 'cdr_db'),
        "PASSWORD": os.environ.get('DB_PASSWORD'),
    }
}


GOOGLE_AUTH_CLIENT_ID = '3n19bi8cv60ohv5m5h27u8t85m'
GOOGLE_AUTH_POOL_ID = 'eu-west-2_VSOPVIoh4'

APPLE_AUTH_CLIENT_ID = '3n19bi8cv60ohv5m5h27u8t85m'
APPLE_AUTH_POOL_ID = 'eu-west-2_VSOPVIoh4'
STRIPE_BETA = True

SLACK_CLIENT_PK_CUTOFF = 2

SEND_SIMP_NOTIFICATIONS = True
AUTO_CREATE_ONBOARDING_DETAILS = True

FORCE_ESIM = True
SIMP_DAILY_POINTS = 100
SIMP_WEEKLY_SPINS = 0  # Disabled by default
