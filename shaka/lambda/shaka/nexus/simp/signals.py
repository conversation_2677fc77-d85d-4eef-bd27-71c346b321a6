import logging
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.conf import settings
from django.db.utils import DatabaseError
from django.utils import timezone
from dateutil.relativedelta import relativedelta
from core.models import (
    Subscriber,
    SimSubscriptionAssignment,
    SubscriptionPayment,
    PerkRedemption,
    Referral
)
from core.time_control import GAMMA_TIME_CONTROL
from .models import Onboarding, Notification, Spin
logger = logging.getLogger(__name__)
@receiver(post_save, sender=Subscriber)
def create_onboarding_details(instance, created, **kwargs):
    if not created:
        return
    if not getattr(settings, 'AUTO_CREATE_ONBOARDING_DETAILS', False):
        return
    try:
        subscriber = instance
        with subscriber.lock():
            _, created = Onboarding.objects.get_or_create(
                subscriber=subscriber,
                defaults={'onboarding_details': {}}
            )
            subscriber.generate_referral_code_if_necessary()
            if created:
                logger.info(
                    "Created onboarding details for subscriber: %s",
                    subscriber.id
                )
    except DatabaseError as err:
        logger.error(
            "Failed to create onboarding details for subscriber %s: %s",
            instance.id,
            str(err)
        )

def _get_next_sunday_midnight(time_control, current_time):
    days_until_sunday = (6 - current_time.weekday()) % 7
    if days_until_sunday == 0:
        days_until_sunday = 7
    next_sunday_datetime = time_control.apply_relative_delta(
        current_time,
        relativedelta(days=days_until_sunday)
    )
    next_sunday_midnight = next_sunday_datetime.replace(hour=0, minute=0, second=0, microsecond=0)
    return next_sunday_midnight
@receiver(post_save, sender=Subscriber)
def create_spin_for_new_subscriber(instance, created, **kwargs):
    if not created:
        return
    try:
        subscriber = instance
        london_now = GAMMA_TIME_CONTROL.ensure_timezone(timezone.now())
        is_monday = london_now.weekday() == 0
        is_after_4am = london_now.hour >= 4
        if is_monday and is_after_4am:
            weekly_spins = getattr(settings, 'SIMP_WEEKLY_SPINS', 0)
            if weekly_spins:
                expiry_datetime = _get_next_sunday_midnight(GAMMA_TIME_CONTROL, london_now)
                with subscriber.lock():
                    for _ in range(int(weekly_spins)):
                        Spin.objects.create(
                            subscriber=subscriber,
                            date_assigned=london_now,
                            expiry_datetime=expiry_datetime
                        )
                    logger.info(
                        "Created %s spins for new subscriber %s (created at %s London time, expires %s)",
                        weekly_spins,
                        subscriber.id,
                        london_now.strftime('%Y-%m-%d %H:%M:%S %Z'),
                        expiry_datetime.strftime('%Y-%m-%d %H:%M:%S %Z')
                    )
            else:
                logger.info(
                    "Skipped spin creation for new subscriber %s - SIMP_WEEKLY_SPINS is %s",
                    subscriber.id,
                    weekly_spins
                )
        else:
            logger.info(
                "Skipped spin creation for new subscriber %s - not after 4am Monday (current: %s London time)",
                subscriber.id,
                london_now.strftime('%Y-%m-%d %H:%M:%S %Z')
            )
    except DatabaseError as err:
        logger.error(
            "Failed to create spin for new subscriber %s: %s",
            instance.id,
            str(err)
        )

@receiver(post_save, sender=SimSubscriptionAssignment)
def update_onboarding_activation_link(instance, created, **kwargs):
    if not created or not instance.sim.is_esim:
        return
    try:
        with instance.subscription.subscriber.lock():
            onboarding = Onboarding.objects.filter(
                subscriber=instance.subscription.subscriber
            ).first()
            if onboarding:
                details = onboarding.onboarding_details or {}
                details['activationLink'] = instance.sim.esim_data
                details['activationCode'] = instance.sim.esim_code
                details['activationAddress'] = instance.sim.esim_address
                onboarding.onboarding_details = details
                onboarding.save()
                logger.info(
                    "Updated onboarding activation link for subscription %s (subscriber: %s)",
                    instance.subscription.id,
                    instance.subscription.subscriber.id
                )
    except DatabaseError as err:
        logger.error(
            "Failed to update onboarding activation link for subscription %s: %s",
            instance.subscription.id,
            str(err)
        )

@receiver(post_save, sender=SubscriptionPayment)
def create_payment_notification(instance, created, **kwargs):
    if not created or not getattr(settings, 'SEND_SIMP_NOTIFICATIONS', False):
        return
    try:
        Notification.objects.create(
            subscriber=instance.subscription.subscriber,
            text="Billing successful!"
        )
        logger.info(
            "Created billing notification for subscription %s (subscriber: %s)",
            instance.subscription.id,
            instance.subscription.subscriber.id
        )
    except DatabaseError as err:
        logger.error(
            "Failed to create billing notification for subscription %s: %s",
            instance.subscription.id,
            str(err)
        )

@receiver(post_save, sender=PerkRedemption)
def create_perk_notification(instance, created, **kwargs):
    if not created or not getattr(settings, 'SEND_SIMP_NOTIFICATIONS', False):
        return
    try:
        Notification.objects.create(
            subscriber=instance.subscriber,
            text="You unlocked a perk!"
        )
        logger.info(
            "Created perk notification for subscriber %s",
            instance.subscriber.id
        )
    except DatabaseError as err:
        logger.error(
            "Failed to create perk notification for subscriber %s: %s",
            instance.subscriber.id,
            str(err)
        )

@receiver(post_save, sender=Referral)
def create_referral_notification(instance, created, **kwargs):
    if not created or not getattr(settings, 'SEND_SIMP_NOTIFICATIONS', False):
        return
    try:
        Notification.objects.create(
            subscriber=instance.referrer,
            text="You referred a friend!"
        )
        logger.info(
            "Created referral notification for referrer %s",
            instance.referrer.id
        )
    except DatabaseError as err:
        logger.error(
            "Failed to create referral notification for referrer %s: %s",
            instance.referrer.id,
            str(err)
        )
