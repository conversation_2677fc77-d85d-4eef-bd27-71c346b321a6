# Generated by Django 4.2.7 on 2025-05-22 19:15

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('simp', '0018_prizetemplate_num_respins_prizetemplate_perk_points'),
    ]

    operations = [
        migrations.AddField(
            model_name='spin',
            name='status',
            field=models.CharField(choices=[('available', 'Available'), ('spinning', 'Spinning'), ('spun', 'Spun')], db_index=True, default='available', max_length=10),
        ),
        migrations.AddIndex(
            model_name='spin',
            index=models.Index(fields=['status'], name='simp_spin_status_b552e2_idx'),
        ),
    ]
