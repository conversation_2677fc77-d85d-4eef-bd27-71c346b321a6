from contextlib import contextmanager
from datetime import datetime, timedelta
from decimal import Decimal
from io import StringIO
from unittest.mock import patch, MagicMock, PropertyMock
from django.core.management import call_command
from django.core.management.base import CommandError
from django.http import HttpRequest
from django.test import override_settings
from django.utils import timezone
from django.db.models import Q
from rest_framework import status
from rest_framework.test import APIClient
from core.time_control import GAMMA_TIME_CONTROL
from core.views import daily_tasks
from core.models import (
    Subscription, SubscriptionPayment, Plan,
    SimPlanAssignment, Provider, SimSubscriptionAssignment, Sim
)
from core.test_utils import NexusTestCase
from core.tests.factories import (
    ClientFactory, SubscriptionFactory, SubscriberFactory, SimFactory,
    SubscriptionPaymentFactory, PerkRedemptionFactory, VoucherPerkFactory
)
from core.tests.test_clock import setup_test_clock, tear_down_test_clock
from simp.views import BearerTokenAuthentication
from simp.models import Onboarding, Notification, Spin, Draw, Prize, PrizeTemplate
from simp.tests.factories import AlertFactory, AlertTargetFactory, NotificationFactory
from subscriber_app.nexus_interface import NexusInterface

class MonkeyPatchSubscriberAuth:
    def __init__(self, subscriber):
        self.subscriber = subscriber
        self.original_has_permission = None

    def __enter__(self):
        self.original_has_permission = getattr(BearerTokenAuthentication, 'has_permission')
        setattr(BearerTokenAuthentication, 'has_permission', self.has_permission)
        return self

    def __exit__(self, *args):  # pylint: disable=unused-argument
        setattr(BearerTokenAuthentication, 'has_permission', self.original_has_permission)

    def has_permission(self, request, _):
        request.access_token = ''
        request.cognito_username = self.subscriber.cognito_username
        return True


@override_settings(AUTO_CREATE_ONBOARDING_DETAILS=True)
class SimpTestCase(NexusTestCase):
    def setUp(self):
        super().setUp()
        self.client = ClientFactory()
        self.subscriber = SubscriberFactory(client=self.client)
        self.client_id = self.client.id

    @contextmanager
    def forced_auth_subscriber(self):
        with MonkeyPatchSubscriberAuth(self.subscriber):
            yield

    def get(self, path):
        client = APIClient()
        client.headers = {'Content-Type': 'application/json'}
        with self.forced_auth_subscriber():
            response = client.get(f'/simp/api/v0/{self.client_id}/{path}')
        return response

    def delete(self, path):
        client = APIClient()
        client.headers = {'Content-Type': 'application/json'}
        with self.forced_auth_subscriber():
            response = client.delete(f'/simp/api/v0/{self.client_id}/{path}')
        return response

    def post(self, path, data):
        client = APIClient()
        client.headers = {'Content-Type': 'application/json'}
        with self.forced_auth_subscriber():
            response = client.post(f'/simp/api/v0/{self.client_id}/{path}', data, format='json')
        return response

class SubscribedSimpTestCase(SimpTestCase):
    def setUp(self):
        super().setUp()
        self.subscription = SubscriptionFactory(using=True, subscriber=self.subscriber)

class AlertTests(SubscribedSimpTestCase):
    def test_alert_visibility_and_dismissal(self):
        alert = AlertFactory(client=self.client, enabled=True)
        AlertTargetFactory(alert=alert, subscriber=self.subscriber)
        response = self.get('alerts/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.json()), 1)
        self.assertEqual(response.json()[0]['id'], alert.id)
        response = self.delete(f'alerts/{alert.id}/')
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        response = self.get('alerts/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.json()), 0)

    def test_disabled_alert_filtering(self):
        alert = AlertFactory(client=self.client, enabled=False)
        AlertTargetFactory(alert=alert, subscriber=self.subscriber)

        response = self.get('alerts/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.json()), 0)

    def test_display_after_constraint(self):
        test_clock = setup_test_clock()
        try:
            future_time = timezone.now() + timedelta(days=1)
            alert = AlertFactory(client=self.client, enabled=True, display_after=future_time)
            AlertTargetFactory(alert=alert, subscriber=self.subscriber)
            response = self.get('alerts/')
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.assertEqual(len(response.json()), 0)

            test_clock.set_date('2025-07-04')
            response = self.get('alerts/')
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.assertEqual(len(response.json()), 1)
        finally:
            tear_down_test_clock()

    def test_create_alert_not_allowed(self):
        response = self.post('alerts/', {})
        self.assertEqual(response.status_code, status.HTTP_405_METHOD_NOT_ALLOWED)

    def test_update_alert_not_allowed(self):
        alert = AlertFactory(client=self.client, enabled=True)
        response = self.post(f'alerts/{alert.id}/', {})
        self.assertEqual(response.status_code, status.HTTP_405_METHOD_NOT_ALLOWED)

class OnboardingSignalTests(SimpTestCase):
    def setUp(self):
        super().setUp()
        Onboarding.objects.all().delete()
    @override_settings(AUTO_CREATE_ONBOARDING_DETAILS=True)

    def test_onboarding_created_when_enabled(self):
        subscriber = SubscriberFactory()
        onboarding = Onboarding.objects.filter(subscriber=subscriber).first()
        self.assertIsNotNone(onboarding)
        self.assertEqual(onboarding.subscriber, subscriber)
        self.assertEqual(onboarding.onboarding_details, {})

    @override_settings(AUTO_CREATE_ONBOARDING_DETAILS=False)
    def test_onboarding_not_created_when_disabled(self):
        subscriber = SubscriberFactory()
        onboarding = Onboarding.objects.filter(subscriber=subscriber).first()
        self.assertIsNone(onboarding)

    @override_settings(AUTO_CREATE_ONBOARDING_DETAILS=True)
    def test_activation_link_updated_for_esim(self):
        subscription = SubscriptionFactory(
            subscriber=SubscriberFactory(),
            billing_subscription_id='test-esim-sub'
        )
        sim = SimFactory(
            esim_data='test-esim-data',
            dispatched_by=self.client,
            status=Sim.SimStatuses.INACTIVE
        )
        SimSubscriptionAssignment.objects.create(
            sim=sim,
            subscription=subscription,
            start_date=timezone.now()
        )
        onboarding = Onboarding.objects.get(subscriber=subscription.subscriber)
        self.assertEqual(
            onboarding.onboarding_details.get('activationLink'),
            'test-esim-data'
        )

class NotificationTests(SimpTestCase):
    def setUp(self):
        super().setUp()
        self.older_notification = NotificationFactory(
            subscriber=self.subscriber,
            text="Seen notification",
            created_at=timezone.now() - timedelta(minutes=30),
            already_seen=True
        )
        self.newer_notification = NotificationFactory(
            subscriber=self.subscriber,
            text="Test notification",
            created_at=timezone.now()
        )

    def test_list_notifications(self):
        response = self.get('notifications/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        self.assertEqual(len(data), 2)
        self.assertEqual(data[0]['text'], self.newer_notification.text)
        self.assertEqual(data[0]['seen'], False)
        self.assertTrue(data[0]['date'] in ['just now', '1 minute ago'])
        self.assertEqual(data[1]['text'], self.older_notification.text)
        self.assertEqual(data[1]['seen'], True)
        self.assertIn('ago', data[1]['date'])

    def test_mark_notification_as_seen(self):
        response = self.delete(f'notifications/{self.newer_notification.id}/')
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.newer_notification.refresh_from_db()
        self.assertTrue(self.newer_notification.seen)

    def test_mark_nonexistent_notification(self):
        response = self.delete('notifications/99999/')
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

class SimpNotificationSignalTests(SimpTestCase):
    def setUp(self):
        super().setUp()
        self.subscription = SubscriptionFactory(
            subscriber=self.subscriber,
            billing_subscription_id='test-sub'
        )

    @override_settings(SEND_SIMP_NOTIFICATIONS=True)
    def test_billing_notification_created(self):
        SubscriptionPaymentFactory(
            subscription=self.subscription,
            amount=1000,
            currency=SubscriptionPayment.Currency.GBP
        )
        notification = Notification.objects.filter(
            subscriber=self.subscriber,
            text="Billing successful!"
        ).first()
        self.assertIsNotNone(notification)
        self.assertFalse(notification.seen)

    @override_settings(SEND_SIMP_NOTIFICATIONS=True)
    def test_perk_notification_created(self):
        perk = VoucherPerkFactory(client=self.client)
        PerkRedemptionFactory(
            subscriber=self.subscriber,
            perk=perk
        )
        notification = Notification.objects.filter(
            subscriber=self.subscriber,
            text="You unlocked a perk!"
        ).first()
        self.assertIsNotNone(notification)
        self.assertFalse(notification.seen)

    @override_settings(SEND_SIMP_NOTIFICATIONS=True)
    def test_referral_notification_created(self):
        referred = SubscriberFactory(client=self.client)
        self.subscriber.referral_code = 'TEST123'
        self.subscriber.save()
        self.subscriber.record_new_referral(referred, 10.00)
        notification = Notification.objects.filter(
            subscriber=self.subscriber,
            text="You referred a friend!"
        ).first()
        self.assertIsNotNone(notification)
        self.assertFalse(notification.seen)

    @override_settings(SEND_SIMP_NOTIFICATIONS=False)
    def test_notifications_not_created_when_disabled(self):
        SubscriptionPaymentFactory(
            subscription=self.subscription,
            amount=1000,
            currency=SubscriptionPayment.Currency.GBP
        )
        self.assertFalse(
            Notification.objects.filter(
                subscriber=self.subscriber,
                text="Billing successful!"
            ).exists()
        )

        perk = VoucherPerkFactory(client=self.client)
        PerkRedemptionFactory(
            subscriber=self.subscriber,
            perk=perk
        )
        self.assertFalse(
            Notification.objects.filter(
                subscriber=self.subscriber,
                text="You unlocked a perk!"
            ).exists()
        )

        referred = SubscriberFactory(client=self.client)
        self.subscriber.referral_code = 'TEST123'
        self.subscriber.save()
        self.subscriber.record_new_referral(referred, 10.00)
        self.assertFalse(
            Notification.objects.filter(
                subscriber=self.subscriber,
                text="You referred a friend!"
            ).exists()
        )

class SpinCreationSignalTests(SimpTestCase):
    def setUp(self):
        super().setUp()
        Spin.objects.all().delete()
    @patch('django.utils.timezone.now')
    @patch('django.conf.settings.SIMP_WEEKLY_SPINS', 1)
    def test_spin_created_for_new_subscriber_after_4am_monday(self, mock_now):
        monday_10am_utc = timezone.datetime(2024, 1, 8, 10, 0, 0, tzinfo=timezone.utc)
        mock_now.return_value = monday_10am_utc
        new_subscriber = SubscriberFactory(client=self.client)
        spin = Spin.objects.filter(subscriber=new_subscriber).first()
        self.assertIsNotNone(spin)
        self.assertEqual(spin.subscriber, new_subscriber)
        self.assertIsNotNone(spin.expiry_datetime)
        expected_expiry = GAMMA_TIME_CONTROL._naive_to_aware(datetime(2024, 1, 14, 0, 0, 0))  # pylint: disable=protected-access
        self.assertEqual(spin.expiry_datetime, expected_expiry)
        self.assertIsNone(spin.prize)

    @patch('django.utils.timezone.now')
    @patch('django.conf.settings.SIMP_WEEKLY_SPINS', 1)
    def test_spin_created_for_new_subscriber_exactly_4am_monday(self, mock_now):
        monday_4am_utc = timezone.datetime(2024, 1, 8, 4, 0, 0, tzinfo=timezone.utc)
        mock_now.return_value = monday_4am_utc
        new_subscriber = SubscriberFactory(client=self.client)
        spin = Spin.objects.filter(subscriber=new_subscriber).first()
        self.assertIsNotNone(spin)
        self.assertEqual(spin.subscriber, new_subscriber)
        # Should have expiry_datetime set to next Sunday midnight
        self.assertIsNotNone(spin.expiry_datetime)
        expected_expiry = GAMMA_TIME_CONTROL._naive_to_aware(datetime(2024, 1, 14, 0, 0, 0))  # pylint: disable=protected-access
        self.assertEqual(spin.expiry_datetime, expected_expiry)

    @patch('django.utils.timezone.now')
    def test_no_spin_created_before_4am_monday(self, mock_now):
        monday_3_59am_utc = timezone.datetime(2024, 1, 8, 3, 59, 0, tzinfo=timezone.utc)
        mock_now.return_value = monday_3_59am_utc
        new_subscriber = SubscriberFactory(client=self.client)
        spin = Spin.objects.filter(subscriber=new_subscriber).first()
        self.assertIsNone(spin)

    @patch('django.utils.timezone.now')
    def test_no_spin_created_on_tuesday(self, mock_now):
        tuesday_10am_utc = timezone.datetime(2024, 1, 9, 10, 0, 0, tzinfo=timezone.utc)
        mock_now.return_value = tuesday_10am_utc
        new_subscriber = SubscriberFactory(client=self.client)
        spin = Spin.objects.filter(subscriber=new_subscriber).first()
        self.assertIsNone(spin)

    @patch('django.utils.timezone.now')
    def test_no_spin_created_on_sunday(self, mock_now):
        sunday_10am_utc = timezone.datetime(2024, 1, 7, 10, 0, 0, tzinfo=timezone.utc)
        mock_now.return_value = sunday_10am_utc
        new_subscriber = SubscriberFactory(client=self.client)
        spin = Spin.objects.filter(subscriber=new_subscriber).first()
        self.assertIsNone(spin)

    @patch('django.utils.timezone.now')
    def test_no_spin_created_for_existing_subscriber_update(self, mock_now):
        monday_10am_utc = timezone.datetime(2024, 1, 8, 10, 0, 0, tzinfo=timezone.utc)
        mock_now.return_value = monday_10am_utc
        existing_subscriber = SubscriberFactory(client=self.client)
        Spin.objects.filter(subscriber=existing_subscriber).delete()
        existing_subscriber.name = "Updated Name"
        existing_subscriber.save()
        spin = Spin.objects.filter(subscriber=existing_subscriber).first()
        self.assertIsNone(spin)

    @patch('django.utils.timezone.now')
    @patch('django.conf.settings.SIMP_WEEKLY_SPINS', 1)
    def test_timezone_handling_with_utc_input(self, mock_now):
        monday_10am_utc = timezone.datetime(2024, 1, 8, 10, 0, 0, tzinfo=timezone.utc)
        mock_now.return_value = monday_10am_utc
        new_subscriber = SubscriberFactory(client=self.client)
        spin = Spin.objects.filter(subscriber=new_subscriber).first()
        self.assertIsNotNone(spin)

    @patch('django.utils.timezone.now')
    @patch('django.conf.settings.SIMP_WEEKLY_SPINS', 1)
    def test_dst_boundary_handling(self, mock_now):
        monday_9am_utc = timezone.datetime(2024, 7, 8, 9, 0, 0, tzinfo=timezone.utc)
        mock_now.return_value = monday_9am_utc
        new_subscriber = SubscriberFactory(client=self.client)
        spin = Spin.objects.filter(subscriber=new_subscriber).first()
        self.assertIsNotNone(spin)

    @patch('django.utils.timezone.now')
    @patch('django.conf.settings.SIMP_WEEKLY_SPINS', 0)
    def test_no_spins_created_when_weekly_spins_is_zero(self, mock_now):
        monday_10am_utc = timezone.datetime(2024, 1, 8, 10, 0, 0, tzinfo=timezone.utc)
        mock_now.return_value = monday_10am_utc
        new_subscriber = SubscriberFactory(client=self.client)
        spins = Spin.objects.filter(subscriber=new_subscriber)
        self.assertEqual(spins.count(), 0)

    @patch('django.utils.timezone.now')
    @patch('django.conf.settings.SIMP_WEEKLY_SPINS', 3)
    def test_multiple_spins_created_when_weekly_spins_is_set(self, mock_now):
        monday_10am_utc = timezone.datetime(2024, 1, 8, 10, 0, 0, tzinfo=timezone.utc)
        mock_now.return_value = monday_10am_utc
        new_subscriber = SubscriberFactory(client=self.client)
        spins = Spin.objects.filter(subscriber=new_subscriber)
        self.assertEqual(spins.count(), 3)
        expiry_times = set(spin.expiry_datetime for spin in spins)
        self.assertEqual(len(expiry_times), 1)

    @patch('django.utils.timezone.now')
    @patch('django.conf.settings.SIMP_WEEKLY_SPINS', 1)
    def test_single_spin_created_when_weekly_spins_is_one(self, mock_now):
        monday_10am_utc = timezone.datetime(2024, 1, 8, 10, 0, 0, tzinfo=timezone.utc)
        mock_now.return_value = monday_10am_utc
        new_subscriber = SubscriberFactory(client=self.client)
        spins = Spin.objects.filter(subscriber=new_subscriber)
        self.assertEqual(spins.count(), 1)

    @patch('django.utils.timezone.now')
    @patch('django.conf.settings.SIMP_WEEKLY_SPINS', '5')
    def test_multiple_spins_with_string_setting(self, mock_now):
        """Test that string values from environment variables are handled correctly"""
        monday_10am_utc = timezone.datetime(2024, 1, 8, 10, 0, 0, tzinfo=timezone.utc)
        mock_now.return_value = monday_10am_utc
        new_subscriber = SubscriberFactory(client=self.client)
        spins = Spin.objects.filter(subscriber=new_subscriber)
        self.assertEqual(spins.count(), 5)

# pylint: disable=too-many-instance-attributes
class CancellationSummaryTests(SimpTestCase):
    def setUp(self):
        super().setUp()
        self.provider = Provider.objects.create(name='Test Provider')
        self.client.provider = self.provider
        self.client.save()
        self.plan = Plan.objects.create(
            client=self.client,
            provider=self.provider,
            points_per_month=100,
            price=20.00,
            name='Test Plan',
            plan_key='test-plan',
            status=Plan.PlanStatuses.ACTIVE
        )
        self.subscription = SubscriptionFactory(
            subscriber=self.subscriber,
            billing_subscription_id='test-sub',
            status=Subscription.Statuses.ACTIVE
        )
        self.sim = SimFactory(status='active')
        SimSubscriptionAssignment.objects.create(
            sim=self.sim,
            subscription=self.subscription,
            start_date=timezone.now()
        )
        SimPlanAssignment.objects.create(
            sim=self.sim,
            plan=self.plan,
            start_date=timezone.now()
        )
        patcher = patch('subscriber_app.nexus_interface.Client.objects.get')
        self.mock_client_get = patcher.start()
        self.mock_client_get.return_value = self.client
        self.addCleanup(patcher.stop)
    @patch('simp.views.NexusInterface')
    @patch('core.models.Provider.time_control')
    def test_get_summary_with_subscription(self, mock_time_control, mock_nexus_class):
        mock_interface = mock_nexus_class.return_value
        mock_end_date = timezone.now() + timedelta(days=30)
        mock_interface.get_subscription_end_date.return_value = mock_end_date.timestamp()
        mock_control = MagicMock()
        mock_control.current_month_datetimes = (timezone.now(), timezone.now() + timedelta(days=31))
        mock_control.get_month_start_and_end_containing.return_value = mock_control.current_month_datetimes
        mock_time_control.return_value = mock_control
        self.subscriber.referral_credit = Decimal("10.00")
        self.subscriber.perk_points = 50
        self.subscriber.save()
        response = self.get('cancellation-summary/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        self.assertEqual(data['referral_credit'], "10.00")
        self.assertEqual(data['perk_points'], 50)
        self.assertIsNotNone(data['cancellation_date'])
        self.assertEqual(len(data['upcoming_rewards']), 0)

    @patch('simp.views.NexusInterface')
    @patch('core.models.Provider.time_control')
    @patch('simp.views.CancellationSummaryView._get_upcoming_rewards')
    def test_get_summary_with_airdrop_perk(self, mock_get_upcoming_rewards, mock_time_control, mock_nexus_class):
        mock_interface = mock_nexus_class.return_value
        mock_interface.get_subscription_end_date.return_value = (timezone.now() + timedelta(days=30)).timestamp()
        mock_control = MagicMock()
        mock_control.current_month_datetimes = (timezone.now(), timezone.now() + timedelta(days=31))
        mock_time_control.return_value = mock_control
        mock_get_upcoming_rewards.return_value = [{
            'id': 1,
            'title': 'Test Airdrop Perk',
            'progress': 60,
            'days_left': 0,
            'image_link': 'https://example.com/image.png',
            'badge': {
                'type': 'perk',
                'text': ''
            }
        }]
        response = self.get('cancellation-summary/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        upcoming_rewards = data['upcoming_rewards']
        self.assertEqual(len(upcoming_rewards), 1)
        self.assertEqual(upcoming_rewards[0]['id'], 1)
        self.assertEqual(upcoming_rewards[0]['days_left'], 0)

    @patch('simp.views.NexusInterface')
    @patch('core.models.Provider.time_control')
    @patch('simp.views.CancellationSummaryView._get_upcoming_rewards')
    def test_get_summary_with_tenure_perk(self, mock_get_upcoming_rewards, mock_time_control, mock_nexus_class):
        mock_time_control.return_value.days_left_this_month = 15
        mock_time_control.return_value.get_complete_months = MagicMock(return_value=3)
        mock_time_control.return_value.current_month_datetimes = (
            timezone.now(), timezone.now() + timedelta(days=31)
        )
        mock_nexus_class.return_value.get_subscription_end_date.return_value = (
            timezone.now() + timedelta(days=30)
        ).timestamp()
        days_left = (2 * 31) + 15
        mock_get_upcoming_rewards.return_value = [{
            'id': 1,
            'title': 'Test Tenure Perk',
            'progress': 60,
            'days_left': days_left,
            'image_link': 'https://example.com/image.png',
            'badge': {
                'type': 'perk',
                'text': ''
            }
        }]
        response = self.get('cancellation-summary/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()['upcoming_rewards'][0]
        self.assertEqual(data['id'], 1)
        self.assertEqual(data['days_left'], days_left)

    @patch('simp.views.NexusInterface')
    @patch('core.models.Provider.time_control')
    @patch('simp.views.CancellationSummaryView._get_upcoming_rewards')
    def test_get_summary_with_total_spend_perk(self, mock_get_upcoming_rewards, mock_time_control, mock_nexus_class):
        mock_interface = mock_nexus_class.return_value
        mock_interface.get_subscription_end_date.return_value = (timezone.now() + timedelta(days=30)).timestamp()
        mock_control = MagicMock()
        mock_control.current_month_datetimes = (timezone.now(), timezone.now() + timedelta(days=31))
        mock_time_control.return_value = mock_control
        days_left = 4 * 31
        mock_get_upcoming_rewards.return_value = [{
            'id': 1,
            'title': 'Test Spend Perk',
            'progress': 60,
            'days_left': days_left,
            'image_link': 'https://example.com/image.png',
            'badge': {
                'type': 'perk',
                'text': ''
            }
        }]
        response = self.get('cancellation-summary/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        upcoming_rewards = data['upcoming_rewards']
        self.assertEqual(len(upcoming_rewards), 1)
        self.assertEqual(upcoming_rewards[0]['id'], 1)
        self.assertEqual(upcoming_rewards[0]['days_left'], days_left)

    @patch('simp.views.NexusInterface')
    @patch('core.models.Provider.time_control')
    @patch('simp.views.CancellationSummaryView._get_upcoming_rewards')
    def test_get_summary_with_points_earned_perk(self, mock_get_upcoming_rewards, mock_time_control, mock_nexus_class):
        mock_interface = mock_nexus_class.return_value
        mock_interface.get_subscription_end_date.return_value = (timezone.now() + timedelta(days=30)).timestamp()
        mock_control = MagicMock()
        mock_control.current_month_datetimes = (timezone.now(), timezone.now() + timedelta(days=31))
        mock_time_control.return_value = mock_control
        days_left = 3 * 31
        mock_get_upcoming_rewards.return_value = [{
            'id': 1,
            'title': 'Test Points Perk',
            'progress': 60,
            'days_left': days_left,
            'image_link': 'https://example.com/image.png',
            'badge': {
                'type': 'perk',
                'text': ''
            }
        }]
        self.subscriber.total_points_earned = 200
        self.subscriber.save()
        self.plan.points_per_month = 100
        self.plan.save()
        response = self.get('cancellation-summary/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        upcoming_rewards = data['upcoming_rewards']
        self.assertEqual(len(upcoming_rewards), 1)
        self.assertEqual(upcoming_rewards[0]['id'], 1)
        self.assertEqual(upcoming_rewards[0]['days_left'], days_left)
    def test_get_summary_without_subscription(self):
        self.subscription.delete()
        response = self.get('cancellation-summary/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        self.assertIsNone(data['cancellation_date'])

    @patch('simp.views.NexusInterface')
    @patch('core.models.Provider.time_control')
    @patch('simp.views.CancellationSummaryView._get_upcoming_rewards')
    def test_get_summary_with_upcoming_rewards(self, mock_get_upcoming_rewards, mock_time_control, mock_nexus_class):
        mock_interface = mock_nexus_class.return_value
        mock_interface.get_subscription_end_date.return_value = (timezone.now() + timedelta(days=30)).timestamp()
        mock_control = MagicMock()
        mock_control.current_month_datetimes = (timezone.now(), timezone.now() + timedelta(days=31))
        mock_time_control.return_value = mock_control
        mock_get_upcoming_rewards.return_value = [{
            'id': 1,
            'title': 'Test Reward',
            'progress': 60,
            'days_left': 5,
            'image_link': 'https://example.com/image.png',
            'badge': {
                'type': 'perk',
                'text': ''
            }
        }]
        response = self.get('cancellation-summary/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        upcoming_rewards = data['upcoming_rewards']
        self.assertEqual(len(upcoming_rewards), 1)
        self.assertEqual(upcoming_rewards[0]['id'], 1)
        self.assertEqual(upcoming_rewards[0]['days_left'], 5)

    @patch('simp.views.NexusInterface')
    @patch('core.models.Provider.time_control')
    @patch('simp.views.CancellationSummaryView._get_upcoming_rewards')
    def test_get_summary_with_already_available_perk(self, mock_get_upcoming_rewards, mock_time_control, mock_nexus_class):
        mock_interface = mock_nexus_class.return_value
        mock_interface.get_subscription_end_date.return_value = (timezone.now() + timedelta(days=30)).timestamp()
        mock_control = MagicMock()
        mock_control.current_month_datetimes = (timezone.now(), timezone.now() + timedelta(days=31))
        mock_time_control.return_value = mock_control
        mock_get_upcoming_rewards.return_value = [{
            'id': 1,
            'title': 'Test Already Available Perk',
            'progress': 60,
            'days_left': 0,
            'image_link': 'https://example.com/image.png',
            'badge': {
                'type': 'perk',
                'text': ''
            }
        }]
        mock_control.get_complete_months = MagicMock(return_value=4)
        type(mock_control).days_left_this_month = PropertyMock(return_value=15)
        response = self.get('cancellation-summary/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        upcoming_rewards = data['upcoming_rewards']
        self.assertEqual(len(upcoming_rewards), 1)
        self.assertEqual(upcoming_rewards[0]['id'], 1)
        self.assertEqual(upcoming_rewards[0]['days_left'], 0)

    @patch('simp.views.NexusInterface')
    @patch('core.models.Provider.time_control')
    @patch('simp.views.CancellationSummaryView._get_upcoming_rewards')
    def test_get_summary_with_next_month_perk(self, mock_get_upcoming_rewards, mock_time_control, mock_nexus_class):
        mock_interface = mock_nexus_class.return_value
        mock_interface.get_subscription_end_date.return_value = (timezone.now() + timedelta(days=30)).timestamp()
        mock_control = MagicMock()
        mock_control.current_month_datetimes = (timezone.now(), timezone.now() + timedelta(days=31))
        mock_time_control.return_value = mock_control
        days_left = 10
        mock_get_upcoming_rewards.return_value = [{
            'id': 1,
            'title': 'Test Next Month Perk',
            'progress': 60,
            'days_left': days_left,
            'image_link': 'https://example.com/image.png',
            'badge': {
                'type': 'perk',
                'text': ''
            }
        }]
        self.subscriber.total_points_earned = 200
        self.subscriber.save()
        self.plan.points_per_month = 100
        self.plan.save()
        mock_control.days_left_this_month = days_left
        response = self.get('cancellation-summary/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        upcoming_rewards = data['upcoming_rewards']
        self.assertEqual(len(upcoming_rewards), 1)
        self.assertEqual(upcoming_rewards[0]['id'], 1)
        self.assertEqual(upcoming_rewards[0]['days_left'], days_left)


class SpinModelTests(SimpTestCase):
    def setUp(self):
        super().setUp()
        self.client_obj = ClientFactory()
        self.subscriber = SubscriberFactory(client=self.client_obj)
        self.draw = Draw.objects.create(
            name="Test Draw",
            client=self.client_obj,
            start_datetime=timezone.now(),
            end_datetime=timezone.now() + timedelta(days=7),
            base_pool_size=10,
            status=Draw.DrawStatus.RUNNING
        )
        self.prize_template = PrizeTemplate.objects.create(
            name="Test Prize",
            description="A test prize",
            is_empty=False
        )
        self.prize = Prize.objects.create(
            draw=self.draw,
            prize_template=self.prize_template
        )
        patcher = patch('subscriber_app.nexus_interface.Client.objects.get')
        self.mock_client_get = patcher.start()
        self.mock_client_get.return_value = self.client_obj
        self.addCleanup(patcher.stop)

    def test_spin_creation_and_properties(self):
        spin = Spin.objects.create(
            subscriber=self.subscriber,
            date_assigned=timezone.now()
        )
        self.assertFalse(spin.is_expired)
        self.assertFalse(spin.is_used)
        self.assertTrue(spin.is_available)
        future_spin = Spin.objects.create(
            subscriber=self.subscriber,
            date_assigned=timezone.now(),
            expiry_datetime=timezone.now() + timedelta(days=1)
        )
        self.assertFalse(future_spin.is_expired)
        self.assertFalse(future_spin.is_used)
        self.assertTrue(future_spin.is_available)
        past_spin = Spin.objects.create(
            subscriber=self.subscriber,
            date_assigned=timezone.now(),
            expiry_datetime=timezone.now() - timedelta(days=1)
        )
        self.assertTrue(past_spin.is_expired)
        self.assertFalse(past_spin.is_used)
        self.assertFalse(past_spin.is_available)
        spin.prize = self.prize
        spin.status = Spin.SpinStatus.SPUN
        spin.save()
        self.assertFalse(spin.is_expired)
        self.assertTrue(spin.is_used)
        self.assertFalse(spin.is_available)

    @patch('simp.models.Prize.lock')
    def test_draw_a_prize_with_available_spin(self, mock_lock):
        mock_context = MagicMock()
        mock_prize = MagicMock()
        mock_prize.is_claimed = False
        mock_context.__enter__.return_value = mock_prize
        mock_lock.return_value = mock_context
        Spin.objects.create(
            subscriber=self.subscriber,
            date_assigned=timezone.now()
        )
        for _ in range(10):
            Prize.objects.create(
                draw=self.draw,
                prize_template=self.prize_template
            )
        with patch('simp.models.Prize.claim') as mock_claim:
            self.draw.draw_a_prize(self.subscriber)
            mock_claim.assert_called_once()
        self.assertEqual(Spin.objects.filter(subscriber=self.subscriber).count(), 1)
    def test_draw_a_prize_without_available_spin(self):
        for _ in range(10):
            Prize.objects.create(
                draw=self.draw,
                prize_template=self.prize_template
            )
        prize = self.draw.draw_a_prize(self.subscriber)
        self.assertIsNone(prize)
    def test_draw_a_prize_with_expired_spin(self):
        for _ in range(10):
            Prize.objects.create(
                draw=self.draw,
                prize_template=self.prize_template
            )
        Spin.objects.create(
            subscriber=self.subscriber,
            date_assigned=timezone.now(),
            expiry_datetime=timezone.now() - timedelta(days=1)
        )
        prize = self.draw.draw_a_prize(self.subscriber)
        self.assertIsNone(prize)

    @patch('simp.models.send_debug_slack_message')
    def test_draw_a_prize_with_no_available_prizes(self, mock_slack):
        Spin.objects.create(
            subscriber=self.subscriber,
            date_assigned=timezone.now()
        )
        self.prize.delete()
        result = self.draw.draw_a_prize(self.subscriber)
        self.assertIsNone(result)
        mock_slack.assert_called_once()
        call_args = mock_slack.call_args[0][0]
        self.assertIn("No prizes available in draw", call_args)
        self.assertIn(self.draw.name, call_args)
        self.assertIn(str(self.draw.id), call_args)
        self.assertIn(self.client_obj.name, call_args)
        self.assertIn(str(self.client_obj.id), call_args)
    def test_spin_methods(self):
        count = Spin.objects.filter(
            subscriber=self.subscriber,
            prize__isnull=True
        ).filter(
            Q(expiry_datetime__isnull=True) | Q(expiry_datetime__gt=timezone.now())
        ).count()
        self.assertEqual(count, 0)
        spin = Spin.objects.create(
            subscriber=self.subscriber,
            date_assigned=timezone.now()
        )
        count = Spin.objects.filter(
            subscriber=self.subscriber,
            prize__isnull=True
        ).filter(
            Q(expiry_datetime__isnull=True) | Q(expiry_datetime__gt=timezone.now())
        ).count()
        self.assertEqual(count, 1)
        expiry_date = timezone.now() + timedelta(days=7)
        expiring_spin = Spin.objects.create(
            subscriber=self.subscriber,
            date_assigned=timezone.now(),
            expiry_datetime=expiry_date
        )
        count = Spin.objects.filter(
            subscriber=self.subscriber,
            prize__isnull=True
        ).filter(
            Q(expiry_datetime__isnull=True) | Q(expiry_datetime__gt=timezone.now())
        ).count()
        self.assertEqual(count, 2)
        spin.prize = self.prize
        spin.status = Spin.SpinStatus.SPUN
        spin.save()
        count = Spin.objects.filter(
            subscriber=self.subscriber,
            prize__isnull=True
        ).filter(
            Q(expiry_datetime__isnull=True) | Q(expiry_datetime__gt=timezone.now())
        ).count()
        self.assertEqual(count, 1)
        self.assertIsNotNone(expiring_spin.expiry_datetime)
        self.assertAlmostEqual(
            expiring_spin.expiry_datetime.timestamp(),
            expiry_date.timestamp(),
            delta=1
        )
    def test_nexus_interface_get_available_spins_count(self):
        nexus = NexusInterface(self.client_obj.id)
        count = nexus.get_available_spins_count(self.subscriber.cognito_username)
        self.assertEqual(count, 0)
        Spin.objects.create(
            subscriber=self.subscriber,
            date_assigned=timezone.now()
        )
        count = nexus.get_available_spins_count(self.subscriber.cognito_username)
        self.assertEqual(count, 1)
        Spin.objects.create(
            subscriber=self.subscriber,
            date_assigned=timezone.now(),
            expiry_datetime=timezone.now() + timedelta(days=7)
        )
        count = nexus.get_available_spins_count(self.subscriber.cognito_username)
        self.assertEqual(count, 2)
        Spin.objects.create(
            subscriber=self.subscriber,
            date_assigned=timezone.now(),
            expiry_datetime=timezone.now() - timedelta(days=1)
        )
        count = nexus.get_available_spins_count(self.subscriber.cognito_username)
        self.assertEqual(count, 2)
        used_spin = Spin.objects.create(
            subscriber=self.subscriber,
            date_assigned=timezone.now()
        )
        used_spin.prize = self.prize
        used_spin.status = Spin.SpinStatus.SPUN
        used_spin.save()
        count = nexus.get_available_spins_count(self.subscriber.cognito_username)
        self.assertEqual(count, 2)

    def test_nexus_interface_allocate_spin(self):
        nexus = NexusInterface(self.client_obj.id)
        spin = nexus.allocate_spin(self.subscriber.cognito_username)
        self.assertIsNotNone(spin)
        self.assertEqual(spin.subscriber, self.subscriber)
        self.assertIsNone(spin.expiry_datetime)
        expiry_days = 7
        spin_with_expiry = nexus.allocate_spin(self.subscriber.cognito_username, expiry_days=expiry_days)
        self.assertIsNotNone(spin_with_expiry)
        self.assertEqual(spin_with_expiry.subscriber, self.subscriber)
        self.assertIsNotNone(spin_with_expiry.expiry_datetime)
        expected_expiry = timezone.now() + timedelta(days=expiry_days)
        self.assertAlmostEqual(
            spin_with_expiry.expiry_datetime.timestamp(),
            expected_expiry.timestamp(),
            delta=5
        )
        count = nexus.get_available_spins_count(self.subscriber.cognito_username)
        self.assertEqual(count, 2)

    def test_draw_api_spins_remaining(self):
        self.client = self.client_obj
        self.client_id = self.client_obj.id
        draw = Draw.objects.create(
            name="API Test Draw",
            client=self.client_obj,
            start_datetime=timezone.now(),
            end_datetime=timezone.now() + timedelta(days=7),
            base_pool_size=10,
            status=Draw.DrawStatus.RUNNING
        )
        Spin.objects.create(
            subscriber=self.subscriber,
            date_assigned=timezone.now()
        )
        Spin.objects.create(
            subscriber=self.subscriber,
            date_assigned=timezone.now()
        )
        client = APIClient()
        client.headers = {'Content-Type': 'application/json'}
        with MonkeyPatchSubscriberAuth(self.subscriber):
            response = client.get(f'/simp/api/v0/{self.client_id}/draws/{draw.id}/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()['spins_remaining'],  2)
        with MonkeyPatchSubscriberAuth(self.subscriber):
            response = client.get(f'/simp/api/v0/{self.client_id}/draws/{draw.id}/')
        self.assertEqual(response.json()['spins_remaining'], 2)
        used_spin = Spin.objects.create(
            subscriber=self.subscriber,
            date_assigned=timezone.now()
        )
        used_spin.prize = self.prize
        used_spin.status = Spin.SpinStatus.SPUN
        used_spin.save()
        Spin.objects.create(
            subscriber=self.subscriber,
            date_assigned=timezone.now(),
            expiry_datetime=timezone.now() - timedelta(days=1)
        )
        with MonkeyPatchSubscriberAuth(self.subscriber):
            response = client.get(f'/simp/api/v0/{self.client_id}/draws/{draw.id}/')
        self.assertEqual(response.json()['spins_remaining'], 2)

    def test_spin_prize_view_no_spins_available(self):
        self.client = self.client_obj
        self.client_id = self.client_obj.id
        draw = Draw.objects.create(
            name="API Test Draw",
            client=self.client_obj,
            start_datetime=timezone.now(),
            end_datetime=timezone.now() + timedelta(days=7),
            base_pool_size=10,
            status=Draw.DrawStatus.RUNNING
        )
        for _ in range(5):
            Prize.objects.create(
                draw=draw,
                prize_template=self.prize_template
            )
        client = APIClient()
        client.headers = {'Content-Type': 'application/json'}
        with MonkeyPatchSubscriberAuth(self.subscriber):
            response = client.post(f'/simp/api/v0/{self.client_id}/draws/{draw.id}/spin/')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.json()['error'], 'NO_SPINS_AVAILABLE')
        self.assertIn('no available spins', response.json()['detail'])

    @patch('core.slack.send_debug_slack_message')
    def test_spin_prize_view_no_prizes_available(self, mock_slack):  # pylint: disable=unused-argument
        self.client = self.client_obj
        self.client_id = self.client_obj.id
        draw = Draw.objects.create(
            name="API Test Draw",
            client=self.client_obj,
            start_datetime=timezone.now(),
            end_datetime=timezone.now() + timedelta(days=7),
            base_pool_size=10,
            status=Draw.DrawStatus.RUNNING
        )
        Spin.objects.create(
            subscriber=self.subscriber,
            date_assigned=timezone.now()
        )
        for _ in range(5):
            prize = Prize.objects.create(
                draw=draw,
                prize_template=self.prize_template
            )
            prize.claimed_by = self.subscriber
            prize.save()

        client = APIClient()
        client.headers = {'Content-Type': 'application/json'}
        with MonkeyPatchSubscriberAuth(self.subscriber):
            response = client.post(f'/simp/api/v0/{self.client_id}/draws/{draw.id}/spin/')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.json()['error'], 'NO_PRIZES_AVAILABLE')
        self.assertIn('No prizes are currently available', response.json()['detail'])

class WeeklySpinsTests(NexusTestCase):
    def setUp(self):
        super().setUp()
        self.client_obj = ClientFactory()
        self.subscriber = SubscriberFactory(client=self.client_obj)

    @patch('core.views.GAMMA_TIME_CONTROL')
    @patch('django.conf.settings.SIMP_WEEKLY_SPINS', 3)
    @patch('django.conf.settings.LAMBDA_API_TOKEN', 'test-token')
    def test_weekly_spins_allocation_on_monday(self, mock_time_control):
        mock_monday = GAMMA_TIME_CONTROL._naive_to_aware(datetime(2024, 3, 4, 10, 0, 0))  # pylint: disable=protected-access
        mock_time_control._now = mock_monday  # pylint: disable=protected-access
        mock_time_control._naive_to_aware = GAMMA_TIME_CONTROL._naive_to_aware  # pylint: disable=protected-access
        expected_expiry = GAMMA_TIME_CONTROL._naive_to_aware(datetime(2024, 3, 10, 0, 0, 0))  # pylint: disable=protected-access
        request = HttpRequest()
        request.method = 'POST'
        request.META['HTTP_AUTHORIZATION'] = 'Bearer test-token'

        response = daily_tasks(request)
        self.assertEqual(response.status_code, 200)
        spins = Spin.objects.filter(subscriber=self.subscriber)
        self.assertEqual(spins.count(), 3)

        for spin in spins:
            self.assertEqual(spin.date_assigned, mock_monday)
            self.assertEqual(spin.expiry_datetime, expected_expiry)
            self.assertIsNone(spin.prize)

    @patch('core.views.GAMMA_TIME_CONTROL')
    @patch('django.conf.settings.SIMP_WEEKLY_SPINS', 2)
    @patch('django.conf.settings.LAMBDA_API_TOKEN', 'test-token')
    def test_no_spins_allocation_on_tuesday(self, mock_time_control):
        mock_tuesday = GAMMA_TIME_CONTROL._naive_to_aware(datetime(2024, 3, 5, 10, 0, 0))  # pylint: disable=protected-access
        mock_time_control._now = mock_tuesday  # pylint: disable=protected-access
        request = HttpRequest()
        request.method = 'POST'
        request.META['HTTP_AUTHORIZATION'] = 'Bearer test-token'
        response = daily_tasks(request)
        self.assertEqual(response.status_code, 200)
        spins = Spin.objects.filter(subscriber=self.subscriber)
        self.assertEqual(spins.count(), 0)

    @patch('core.views.GAMMA_TIME_CONTROL')
    @patch('django.conf.settings.SIMP_WEEKLY_SPINS', 1)
    @patch('django.conf.settings.LAMBDA_API_TOKEN', 'test-token')
    def test_weekly_spins_dst_transition(self, mock_time_control):
        mock_monday = GAMMA_TIME_CONTROL._naive_to_aware(datetime(2024, 3, 25, 10, 0, 0))  # pylint: disable=protected-access
        mock_time_control._now = mock_monday  # pylint: disable=protected-access
        mock_time_control._naive_to_aware = GAMMA_TIME_CONTROL._naive_to_aware  # pylint: disable=protected-access
        expected_expiry = GAMMA_TIME_CONTROL._naive_to_aware(datetime(2024, 3, 31, 0, 0, 0))  # pylint: disable=protected-access
        request = HttpRequest()
        request.method = 'POST'
        request.META['HTTP_AUTHORIZATION'] = 'Bearer test-token'
        response = daily_tasks(request)
        self.assertEqual(response.status_code, 200)
        spins = Spin.objects.filter(subscriber=self.subscriber)
        self.assertEqual(spins.count(), 1)
        spin = spins.first()
        self.assertEqual(spin.date_assigned, mock_monday)
        self.assertEqual(spin.expiry_datetime, expected_expiry)

    @patch('django.conf.settings.SIMP_WEEKLY_SPINS', 0)
    @patch('django.conf.settings.LAMBDA_API_TOKEN', 'test-token')
    def test_no_spins_when_setting_is_zero(self):
        request = HttpRequest()
        request.method = 'POST'
        request.META['HTTP_AUTHORIZATION'] = 'Bearer test-token'
        response = daily_tasks(request)
        self.assertEqual(response.status_code, 200)
        spins = Spin.objects.filter(subscriber=self.subscriber)
        self.assertEqual(spins.count(), 0)

class SpinStatusAndLockingTests(NexusTestCase):
    def setUp(self):
        super().setUp()
        self.client_obj = ClientFactory()
        self.subscriber = SubscriberFactory(client=self.client_obj)
        self.draw = Draw.objects.create(
            name="Test Draw",
            client=self.client_obj,
            start_datetime=timezone.now(),
            end_datetime=timezone.now() + timedelta(days=7),
            base_pool_size=10,
            new_subscriber_buffer=0,
            respin_buffer=0,
            status=Draw.DrawStatus.RUNNING
        )
        self.prize_template = PrizeTemplate.objects.create(
            name="Test Prize",
            description="A test prize",
            is_empty=False
        )
        self.prizes = []
        for _ in range(10):
            prize = Prize.objects.create(
                draw=self.draw,
                prize_template=self.prize_template
            )
            self.prizes.append(prize)
        self.prize = self.prizes[0]

    def test_spin_status_field_default(self):
        spin = Spin.objects.create(subscriber=self.subscriber)
        self.assertEqual(spin.status, Spin.SpinStatus.AVAILABLE)
        self.assertTrue(spin.is_available)
        self.assertFalse(spin.is_used)

    def test_spin_status_transitions(self):
        spin = Spin.objects.create(subscriber=self.subscriber)
        self.assertEqual(spin.status, Spin.SpinStatus.AVAILABLE)
        self.assertTrue(spin.is_available)
        spin.mark_as_spinning()
        spin.refresh_from_db()
        self.assertEqual(spin.status, Spin.SpinStatus.SPINNING)
        self.assertFalse(spin.is_available)
        self.assertFalse(spin.is_used)
        spin.mark_as_spun(self.prize)
        spin.refresh_from_db()
        self.assertEqual(spin.status, Spin.SpinStatus.SPUN)
        self.assertFalse(spin.is_available)
        self.assertTrue(spin.is_used)
        self.assertEqual(spin.prize, self.prize)

    def test_spin_locking_mechanism(self):
        spin = Spin.objects.create(subscriber=self.subscriber)

        with spin.lock() as locked_spin:
            self.assertEqual(locked_spin.id, spin.id)
            self.assertEqual(locked_spin.status, Spin.SpinStatus.AVAILABLE)
            locked_spin.mark_as_spinning()
        spin.refresh_from_db()
        self.assertEqual(spin.status, Spin.SpinStatus.SPINNING)

    def test_draw_prize_with_status_field(self):
        spin = Spin.objects.create(subscriber=self.subscriber)
        self.assertEqual(spin.status, Spin.SpinStatus.AVAILABLE)
        result = self.draw.draw_a_prize(self.subscriber)
        self.assertIsNotNone(result)
        self.assertIn(result, self.prizes)
        self.assertEqual(result.prize_template, self.prize_template)
        spin.refresh_from_db()
        self.assertEqual(spin.status, Spin.SpinStatus.SPUN)
        self.assertEqual(spin.prize, result)

    def test_draw_prize_no_available_spins_with_status(self):
        spin = Spin.objects.create(subscriber=self.subscriber)
        spin.mark_as_spinning()
        result = self.draw.draw_a_prize(self.subscriber)
        self.assertIsNone(result)
        spin.refresh_from_db()
        self.assertEqual(spin.status, Spin.SpinStatus.SPINNING)

    def test_draw_prize_with_expired_spin(self):
        expired_time = timezone.now() - timedelta(hours=1)
        spin = Spin.objects.create(
            subscriber=self.subscriber,
            expiry_datetime=expired_time
        )
        self.assertFalse(spin.is_available)
        result = self.draw.draw_a_prize(self.subscriber)
        self.assertIsNone(result)

    def test_serializer_uses_status_field(self):
        from simp.serializers import DrawSerializer  # pylint: disable=import-outside-toplevel
        Spin.objects.create(subscriber=self.subscriber)  # available spin
        spinning_spin = Spin.objects.create(subscriber=self.subscriber)
        spinning_spin.mark_as_spinning()
        spun_spin = Spin.objects.create(subscriber=self.subscriber)
        spun_spin.mark_as_spun(self.prize)
        serializer = DrawSerializer(self.draw, context={'subscriber': self.subscriber})
        data = serializer.data
        self.assertEqual(data['spins_remaining'], 1)

    def test_concurrent_spin_access_simulation(self):
        spin = Spin.objects.create(subscriber=self.subscriber)
        with spin.lock() as locked_spin:
            self.assertEqual(locked_spin.status, Spin.SpinStatus.AVAILABLE)
            locked_spin.mark_as_spinning()
            spin.refresh_from_db()
            self.assertEqual(spin.status, Spin.SpinStatus.SPINNING)
            self.assertFalse(spin.is_available)

class FillEmptyPrizesCommandTest(NexusTestCase):
    def setUp(self):
        super().setUp()
        self.client_obj = ClientFactory()
        self.draw = Draw.objects.create(
            name="Test Draw",
            client=self.client_obj,
            start_datetime=timezone.now(),
            end_datetime=timezone.now() + timedelta(days=7),
            base_pool_size=100,
            new_subscriber_buffer=0,
            respin_buffer=0,
            status=Draw.DrawStatus.DRAFT
        )
        self.empty_template = PrizeTemplate.objects.create(
            name="Empty Prize",
            description="An empty prize",
            is_empty=True
        )
        self.real_template = PrizeTemplate.objects.create(
            name="Real Prize",
            description="A real prize",
            is_empty=False
        )
        for _ in range(10):
            Prize.objects.create(
                draw=self.draw,
                prize_template=self.real_template
            )
    def test_command_creates_empty_prizes(self):
        out = StringIO()
        call_command('fill_empty_prizes', self.draw.id, stdout=out)
        self.assertEqual(Prize.objects.filter(draw=self.draw).count(), 100)
        self.assertEqual(Prize.objects.filter(draw=self.draw, prize_template=self.empty_template).count(), 90)
        output = out.getvalue()
        self.assertIn(f'Successfully created 90 empty prizes for draw {self.draw.name}', output)
    def test_command_with_nonexistent_draw(self):
        with self.assertRaises(CommandError):
            call_command('fill_empty_prizes', 999)
    def test_command_with_no_empty_template(self):
        self.empty_template.delete()
        with self.assertRaises(CommandError):
            call_command('fill_empty_prizes', self.draw.id)
    def test_command_with_full_draw(self):
        for _ in range(90):
            Prize.objects.create(
                draw=self.draw,
                prize_template=self.real_template
            )
        out = StringIO()
        call_command('fill_empty_prizes', self.draw.id, stdout=out)
        self.assertEqual(Prize.objects.filter(draw=self.draw).count(), 100)
        output = out.getvalue()
        self.assertIn(f'Draw {self.draw.name} already has 100 prizes', output)
