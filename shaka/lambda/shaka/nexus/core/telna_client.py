from decimal import Decimal
import logging
import requests
from django.conf import settings
from .utils import calculate_luhn


logger = logging.getLogger()


class TelnaClient:
    def _get_access_token(self):
        return settings.TELNA_API_KEY

    def clean_iccid(self, iccid):
        if calculate_luhn(iccid[:-1]) == int(iccid[-1]):
            return iccid[:-1]
        return iccid

    def _post(self, path, data):
        access_token = self._get_access_token()
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }
        response = requests.post(f'https://developer-api.telna.com{path}', json=data, headers=headers, timeout=20)
        response.raise_for_status()
        return response.json()

    def _get(self, path):
        access_token = self._get_access_token()
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }
        response = requests.get(f'https://developer-api.telna.com{path}', headers=headers, timeout=20)
        response.raise_for_status()
        return response.json()

    def create_package(self, sim_iccid, package_template_id):
        return self._post('/v2.1/pcr/packages', {
            'sim': self.clean_iccid(sim_iccid),
            'package_template': package_template_id
        })['id']

    def get_data_usage_and_allowance(self, package_id):
        resp = self._get(f'/v2.1/pcr/packages/{package_id}')
        allowance = int(Decimal(resp['package_template']['data_usage_allowance']))
        remaining = int(Decimal(resp['data_usage_remaining']))
        return allowance - remaining, allowance
