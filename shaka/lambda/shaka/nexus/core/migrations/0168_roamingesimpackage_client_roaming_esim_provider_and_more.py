# Generated by Django 4.2.7 on 2025-04-03 22:59

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0167_merge_20250403_2359'),
    ]

    operations = [
        migrations.CreateModel(
            name='RoamingEsimPackage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('cost', models.DecimalField(decimal_places=2, help_text='Cost to us', max_digits=10)),
                ('data_limit_gb', models.DecimalField(decimal_places=2, default=0, help_text='Data limit in GB, 0 means unlimited', max_digits=5)),
                ('price', models.DecimalField(decimal_places=2, max_digits=6)),
                ('days', models.PositiveIntegerField(default=1, help_text='Number of days the package is valid for')),
                ('provider_code', models.CharField(blank=True, max_length=50, null=True)),
                ('enabled', models.BooleanField(default=False)),
                ('billing_price_id', models.CharField(blank=True, max_length=50, null=True)),
            ],
        ),
        migrations.AddField(
            model_name='client',
            name='roaming_esim_provider',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='roaming_esim_clients', to='core.provider'),
        ),
        migrations.AddField(
            model_name='sim',
            name='roaming_esim_available_to',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='roaming_esims', to='core.client'),
        ),
        migrations.AlterField(
            model_name='client',
            name='provider',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='clients', to='core.provider'),
        ),
        migrations.AlterField(
            model_name='subscription',
            name='subscription_type',
            field=models.CharField(choices=[('primary', 'Primary'), ('roaming_esim', 'Roaming eSIM')], default='primary', max_length=20),
        ),
        migrations.CreateModel(
            name='RoamingEsimRegion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, unique=True)),
                ('packages', models.ManyToManyField(related_name='regions', to='core.roamingesimpackage')),
            ],
        ),
        migrations.CreateModel(
            name='RoamingEsimPurchase',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('purchase_datetime', models.DateTimeField(default=django.utils.timezone.now)),
                ('purchase_country_code', models.CharField(blank=True, max_length=2, null=True)),
                ('purchase_zone_name', models.CharField(blank=True, max_length=255, null=True)),
                ('amount_paid', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('package', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='purchases', to='core.roamingesimpackage')),
                ('subscription', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='roaming_esim_purchases', to='core.subscription')),
            ],
        ),
        migrations.AddField(
            model_name='roamingesimpackage',
            name='client',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='roaming_esim_packages', to='core.client'),
        ),
        migrations.CreateModel(
            name='RoamingEsimCountry',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('country_code', models.CharField(choices=[('GB', 'United Kingdom'), ('US', 'United States')], max_length=2)),
                ('popularity', models.IntegerField(default=0, help_text='Higher means more popular')),
                ('package', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='countries', to='core.roamingesimpackage')),
            ],
        ),
    ]
