# Generated by Django 4.2.7 on 2025-04-09 13:29

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0170_rename_billing_price_id_roamingesimpackage_billing_product_id'),
    ]

    operations = [
        migrations.CreateModel(
            name='SimBatch',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
            ],
        ),
        migrations.AddField(
            model_name='subscription',
            name='provider_reference',
            field=models.CharField(blank=True, default='', help_text='e.g. Telna package id', max_length=100),
        ),
        migrations.AddField(
            model_name='roamingesimpackage',
            name='sim_batch',
            field=models.ForeignKey(blank=True, help_text='Batch of SIMs (e.g. Nordic profile type. If null then non-batch esims will be used', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='roaming_esim_packages', to='core.simbatch'),
        ),
        migrations.AddField(
            model_name='sim',
            name='batch',
            field=models.ForeignKey(blank=True, help_text='The batch (e.g. nordic profile type) this sim belongs to', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='sims', to='core.simbatch'),
        ),
    ]
