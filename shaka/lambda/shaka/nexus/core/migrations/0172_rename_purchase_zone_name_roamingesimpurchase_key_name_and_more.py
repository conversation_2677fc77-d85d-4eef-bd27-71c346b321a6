# Generated by Django 4.2.7 on 2025-04-14 23:24

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0171_simbatch_subscription_provider_reference_and_more'),
    ]

    operations = [
        migrations.RenameField(
            model_name='roamingesimpurchase',
            old_name='purchase_zone_name',
            new_name='key_name',
        ),
        migrations.RemoveField(
            model_name='roamingesimpurchase',
            name='purchase_country_code',
        ),
        migrations.RemoveField(
            model_name='subscription',
            name='provider_reference',
        ),
        migrations.AddField(
            model_name='roamingesimpurchase',
            name='provider_reference',
            field=models.CharField(blank=True, default='', help_text='e.g. Telna package id', max_length=100),
        ),
    ]
